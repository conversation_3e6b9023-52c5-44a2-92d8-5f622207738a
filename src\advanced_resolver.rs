use crate::errors::{NxError, Result};
use crate::registry::{Registry, PackageMetadata};
use semver::{Version, VersionReq};
use std::collections::{HashMap, HashSet, VecDeque, BTreeMap};
use tracing::{debug, info, warn};
use indicatif::{ProgressBar, ProgressStyle};
use dashmap::DashMap;
use parking_lot::RwLock;
use std::sync::Arc;
use rayon::prelude::*;

#[derive(Debug, Clone)]
pub struct ResolvedDependency {
    pub name: String,
    pub version: String,
    pub resolved_url: String,
    pub integrity: Option<String>,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
}

#[derive(Debug)]
pub struct ResolutionResult {
    pub resolved: HashMap<String, ResolvedDependency>,
    pub install_order: Vec<String>,
    pub conflicts: Vec<DependencyConflict>,
    pub warnings: Vec<String>,
}

#[derive(Debug)]
pub struct DependencyConflict {
    pub package: String,
    pub requested_versions: Vec<String>,
    pub resolved_version: String,
    pub reason: String,
}

pub struct AdvancedResolver {
    registry: Registry,
    metadata_cache: Arc<DashMap<String, PackageMetadata>>,
    resolution_cache: Arc<DashMap<String, String>>,
    version_cache: Arc<DashMap<String, Vec<Version>>>,
    conflict_resolver: ConflictResolver,
}

struct ConflictResolver {
    strategies: Vec<ConflictStrategy>,
}

#[derive(Debug, Clone)]
enum ConflictStrategy {
    PreferLatest,
    PreferSemverCompatible,
    PreferExisting,
    PreferDirect,
}

impl ConflictResolver {
    fn new() -> Self {
        Self {
            strategies: vec![
                ConflictStrategy::PreferDirect,
                ConflictStrategy::PreferSemverCompatible,
                ConflictStrategy::PreferLatest,
            ],
        }
    }

    fn resolve_conflict(
        &self,
        package_name: &str,
        conflicting_versions: &[String],
        requirements: &[VersionReq],
    ) -> Result<String> {
        // Parse versions
        let mut versions: Vec<Version> = conflicting_versions
            .iter()
            .filter_map(|v| Version::parse(v).ok())
            .collect();

        if versions.is_empty() {
            return Err(NxError::VersionNotFound {
                package: package_name.to_string(),
                version: conflicting_versions.join(", "),
            });
        }

        // Apply conflict resolution strategies
        for strategy in &self.strategies {
            if let Some(resolved) = self.apply_strategy(strategy, &versions, requirements) {
                return Ok(resolved.to_string());
            }
        }

        // Fallback to latest version
        versions.sort();
        Ok(versions.last().unwrap().to_string())
    }

    fn apply_strategy(
        &self,
        strategy: &ConflictStrategy,
        versions: &[Version],
        requirements: &[VersionReq],
    ) -> Option<Version> {
        match strategy {
            ConflictStrategy::PreferLatest => {
                let mut sorted = versions.to_vec();
                sorted.sort();
                sorted.reverse();

                for version in sorted {
                    if requirements.iter().all(|req| req.matches(&version)) {
                        return Some(version);
                    }
                }
                None
            }
            ConflictStrategy::PreferSemverCompatible => {
                // Find version that satisfies most requirements
                let mut best_version = None;
                let mut best_score = 0;

                for version in versions {
                    let score = requirements.iter()
                        .filter(|req| req.matches(version))
                        .count();

                    if score > best_score {
                        best_score = score;
                        best_version = Some(version.clone());
                    }
                }
                best_version
            }
            ConflictStrategy::PreferExisting => {
                // Prefer already resolved versions
                versions.first().cloned()
            }
            ConflictStrategy::PreferDirect => {
                // Prefer directly specified dependencies
                versions.first().cloned()
            }
        }
    }
}

impl AdvancedResolver {
    pub fn new() -> Self {
        Self {
            registry: Registry::new(),
            metadata_cache: Arc::new(DashMap::new()),
            resolution_cache: Arc::new(DashMap::new()),
            version_cache: Arc::new(DashMap::new()),
            conflict_resolver: ConflictResolver::new(),
        }
    }

    pub async fn resolve_dependencies_parallel(
        &self,
        root_deps: &HashMap<String, String>,
        dev_deps: &HashMap<String, String>,
        include_dev: bool,
    ) -> Result<ResolutionResult> {
        info!("🔍 Starting parallel dependency resolution...");

        let pb = ProgressBar::new_spinner();
        pb.set_style(
            ProgressStyle::default_spinner()
                .template("{spinner:.cyan} {msg}")
                .unwrap()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]),
        );
        pb.set_message("Building dependency graph...");
        pb.enable_steady_tick(std::time::Duration::from_millis(100));

        // Build initial dependency list
        let mut all_deps = root_deps.clone();
        if include_dev {
            all_deps.extend(dev_deps.clone());
        }

        // Parallel metadata fetching
        let metadata_futures: Vec<_> = all_deps.keys()
            .map(|name| self.fetch_metadata_cached(name.clone()))
            .collect();

        let metadata_results = futures::future::join_all(metadata_futures).await;

        // Process results and build dependency graph
        let mut resolved = HashMap::new();
        let mut conflicts = Vec::new();
        let mut warnings = Vec::new();
        let mut dependency_graph = DependencyGraph::new();

        for (i, result) in metadata_results.into_iter().enumerate() {
            let package_name = all_deps.keys().nth(i).unwrap();
            let version_req = all_deps.get(package_name).unwrap();

            match result {
                Ok(metadata) => {
                    match self.resolve_version_smart(&metadata, version_req) {
                        Ok(resolved_version) => {
                            let resolved_dep = self.create_resolved_dependency(
                                package_name,
                                &resolved_version,
                                &metadata,
                            )?;

                            dependency_graph.add_node(package_name.clone(), resolved_dep.clone());
                            resolved.insert(package_name.clone(), resolved_dep);
                        }
                        Err(e) => {
                            warnings.push(format!("Failed to resolve {}: {}", package_name, e));
                        }
                    }
                }
                Err(e) => {
                    warnings.push(format!("Failed to fetch metadata for {}: {}", package_name, e));
                }
            }
        }

        pb.set_message("Resolving transitive dependencies...");

        // Resolve transitive dependencies in parallel
        self.resolve_transitive_dependencies(&mut resolved, &mut dependency_graph, &mut conflicts, &mut warnings).await?;

        pb.set_message("Calculating install order...");

        // Calculate optimal install order
        let install_order = dependency_graph.topological_sort()?;

        pb.finish_with_message(format!("✅ Resolved {} packages", resolved.len()));

        Ok(ResolutionResult {
            resolved,
            install_order,
            conflicts,
            warnings,
        })
    }

    pub async fn resolve_dependencies(
        &mut self,
        root_deps: &HashMap<String, String>,
        dev_deps: &HashMap<String, String>,
        include_dev: bool,
    ) -> Result<ResolutionResult> {
        info!("🔍 Starting advanced dependency resolution...");
        
        let pb = ProgressBar::new_spinner();
        pb.set_style(
            ProgressStyle::default_spinner()
                .template("{spinner:.cyan} {msg}")
                .unwrap()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]),
        );
        pb.set_message("Resolving dependency tree...");
        pb.enable_steady_tick(std::time::Duration::from_millis(100));

        let mut resolved = HashMap::new();
        let mut conflicts = Vec::new();
        let mut warnings = Vec::new();
        let mut to_resolve = VecDeque::new();
        let mut visited = HashSet::new();

        // Add root dependencies
        for (name, version_req) in root_deps {
            to_resolve.push_back((name.clone(), version_req.clone(), 0, false));
        }

        // Add dev dependencies if requested
        if include_dev {
            for (name, version_req) in dev_deps {
                to_resolve.push_back((name.clone(), version_req.clone(), 0, true));
            }
        }

        let mut processed = 0;
        const MAX_DEPTH: usize = 20;
        const MAX_PACKAGES: usize = 10000;

        while let Some((package_name, version_req, depth, is_dev)) = to_resolve.pop_front() {
            processed += 1;
            
            if processed > MAX_PACKAGES {
                warn!("Reached maximum package limit, stopping resolution");
                break;
            }

            if depth > MAX_DEPTH {
                warn!("Maximum dependency depth reached for {}", package_name);
                continue;
            }

            let package_key = format!("{}@{}", package_name, version_req);
            if visited.contains(&package_key) {
                continue;
            }
            visited.insert(package_key.clone());

            pb.set_message(format!("Resolving {} (depth: {})", package_name, depth));

            // Try to resolve from cache first
            if let Some(cached_version) = self.resolution_cache.get(&package_key) {
                debug!("Using cached resolution for {}: {}", package_name, cached_version);
                continue;
            }

            // Fetch package metadata
            let metadata = match self.get_package_metadata(&package_name).await {
                Ok(meta) => meta,
                Err(e) => {
                    warnings.push(format!("Failed to fetch {}: {}", package_name, e));
                    continue;
                }
            };

            // Resolve version
            let resolved_version = match self.resolve_version(&metadata, &version_req) {
                Ok(version) => version,
                Err(e) => {
                    warnings.push(format!("Failed to resolve version for {}@{}: {}", package_name, version_req, e));
                    continue;
                }
            };

            let resolved_key = format!("{}@{}", package_name, resolved_version);
            
            // Check for conflicts
            if let Some(existing) = resolved.get(&package_name) {
                if existing.version != resolved_version {
                    conflicts.push(DependencyConflict {
                        package: package_name.clone(),
                        requested_versions: vec![existing.version.clone(), resolved_version.clone()],
                        resolved_version: existing.version.clone(),
                        reason: "Version conflict detected".to_string(),
                    });
                    continue;
                }
            }

            // Get version info
            let version_info = match metadata.versions.get(&resolved_version) {
                Some(info) => info,
                None => {
                    warnings.push(format!("Version info not found for {}@{}", package_name, resolved_version));
                    continue;
                }
            };

            // Create resolved dependency
            let resolved_dep = ResolvedDependency {
                name: package_name.clone(),
                version: resolved_version.clone(),
                resolved_url: version_info.dist.tarball.clone(),
                integrity: version_info.dist.integrity.clone(),
                dependencies: version_info.dependencies.clone().unwrap_or_default(),
                dev_dependencies: version_info.dev_dependencies.clone().unwrap_or_default(),
                peer_dependencies: version_info.peer_dependencies.clone().unwrap_or_default(),
                optional_dependencies: HashMap::new(), // TODO: Add optional deps support
            };

            // Cache the resolution
            self.resolution_cache.insert(package_key, resolved_version.clone());

            // Add child dependencies to queue
            if depth < MAX_DEPTH {
                for (dep_name, dep_version) in &resolved_dep.dependencies {
                    if dep_name != &package_name { // Avoid self-dependencies
                        to_resolve.push_back((dep_name.clone(), dep_version.clone(), depth + 1, false));
                    }
                }

                // Add peer dependencies as warnings if not satisfied
                for (peer_name, peer_version) in &resolved_dep.peer_dependencies {
                    if !resolved.contains_key(peer_name) {
                        warnings.push(format!("Peer dependency {} required by {} not found", peer_name, package_name));
                    }
                }
            }

            resolved.insert(package_name.clone(), resolved_dep);
        }

        pb.finish_with_message(format!("✅ Resolved {} packages", resolved.len()));

        // Calculate install order
        let install_order = self.calculate_install_order(&resolved)?;

        Ok(ResolutionResult {
            resolved,
            install_order,
            conflicts,
            warnings,
        })
    }

    async fn get_package_metadata(&mut self, package_name: &str) -> Result<&PackageMetadata> {
        if !self.cache.contains_key(package_name) {
            let metadata = self.registry.get_package_metadata(package_name).await?;
            self.cache.insert(package_name.to_string(), metadata);
        }
        Ok(self.cache.get(package_name).unwrap())
    }

    fn resolve_version(&self, metadata: &PackageMetadata, version_req: &str) -> Result<String> {
        // Handle dist-tags
        if let Some(version) = metadata.dist_tags.get(version_req) {
            return Ok(version.clone());
        }

        // Parse semver requirement
        let req = VersionReq::parse(version_req)?;
        
        // Get all versions and sort them
        let mut versions: Vec<Version> = metadata
            .versions
            .keys()
            .filter_map(|v| Version::parse(v).ok())
            .collect();
        
        versions.sort();
        versions.reverse(); // Prefer newer versions

        // Find the best matching version
        for version in versions {
            if req.matches(&version) {
                return Ok(version.to_string());
            }
        }

        Err(NxError::VersionNotFound {
            package: metadata.name.clone(),
            version: version_req.to_string(),
        })
    }

    async fn fetch_metadata_cached(&self, package_name: String) -> Result<PackageMetadata> {
        // Check cache first
        if let Some(metadata) = self.metadata_cache.get(&package_name) {
            return Ok(metadata.clone());
        }

        // Fetch from registry
        let metadata = self.registry.get_package_metadata(&package_name).await?;
        self.metadata_cache.insert(package_name, metadata.clone());
        Ok(metadata)
    }

    fn resolve_version_smart(&self, metadata: &PackageMetadata, version_req: &str) -> Result<String> {
        // Check resolution cache
        let cache_key = format!("{}@{}", metadata.name, version_req);
        if let Some(cached) = self.resolution_cache.get(&cache_key) {
            return Ok(cached.clone());
        }

        // Handle dist-tags (latest, next, beta, etc.)
        if let Some(version) = metadata.dist_tags.get(version_req) {
            self.resolution_cache.insert(cache_key, version.clone());
            return Ok(version.clone());
        }

        // Parse semver requirement
        let req = VersionReq::parse(version_req)
            .map_err(|_| NxError::VersionNotFound {
                package: metadata.name.clone(),
                version: version_req.to_string(),
            })?;

        // Get cached versions or parse them
        let versions = self.get_sorted_versions(&metadata.name, &metadata.versions)?;

        // Find best matching version (prefer latest compatible)
        for version in versions.iter().rev() {
            if req.matches(version) {
                let version_str = version.to_string();
                self.resolution_cache.insert(cache_key, version_str.clone());
                return Ok(version_str);
            }
        }

        Err(NxError::VersionNotFound {
            package: metadata.name.clone(),
            version: version_req.to_string(),
        })
    }

    fn get_sorted_versions(&self, package_name: &str, versions_map: &HashMap<String, crate::registry::VersionInfo>) -> Result<Vec<Version>> {
        // Check cache
        if let Some(cached) = self.version_cache.get(package_name) {
            return Ok(cached.clone());
        }

        // Parse and sort versions
        let mut versions: Vec<Version> = versions_map
            .keys()
            .filter_map(|v| Version::parse(v).ok())
            .collect();

        versions.sort();
        self.version_cache.insert(package_name.to_string(), versions.clone());
        Ok(versions)
    }

    fn create_resolved_dependency(
        &self,
        name: &str,
        version: &str,
        metadata: &PackageMetadata,
    ) -> Result<ResolvedDependency> {
        let version_info = metadata.versions.get(version)
            .ok_or_else(|| NxError::VersionNotFound {
                package: name.to_string(),
                version: version.to_string(),
            })?;

        Ok(ResolvedDependency {
            name: name.to_string(),
            version: version.to_string(),
            resolved_url: version_info.dist.tarball.clone(),
            integrity: version_info.dist.integrity.clone(),
            dependencies: version_info.dependencies.clone().unwrap_or_default(),
            dev_dependencies: version_info.dev_dependencies.clone().unwrap_or_default(),
            peer_dependencies: version_info.peer_dependencies.clone().unwrap_or_default(),
            optional_dependencies: version_info.optional_dependencies.clone().unwrap_or_default(),
        })
    }

    async fn resolve_transitive_dependencies(
        &self,
        resolved: &mut HashMap<String, ResolvedDependency>,
        graph: &mut DependencyGraph,
        conflicts: &mut Vec<DependencyConflict>,
        warnings: &mut Vec<String>,
    ) -> Result<()> {
        let mut to_process = VecDeque::new();
        let mut processed = HashSet::new();

        // Add initial dependencies to process
        for dep in resolved.values() {
            for (dep_name, dep_version) in &dep.dependencies {
                if !resolved.contains_key(dep_name) {
                    to_process.push_back((dep_name.clone(), dep_version.clone(), 1));
                }
            }
        }

        const MAX_DEPTH: usize = 50;

        while let Some((package_name, version_req, depth)) = to_process.pop_front() {
            if depth > MAX_DEPTH {
                warnings.push(format!("Max depth reached for {}", package_name));
                continue;
            }

            let process_key = format!("{}@{}", package_name, version_req);
            if processed.contains(&process_key) {
                continue;
            }
            processed.insert(process_key);

            match self.fetch_metadata_cached(package_name.clone()).await {
                Ok(metadata) => {
                    match self.resolve_version_smart(&metadata, &version_req) {
                        Ok(resolved_version) => {
                            // Check for conflicts
                            if let Some(existing) = resolved.get(&package_name) {
                                if existing.version != resolved_version {
                                    // Try to resolve conflict
                                    let conflicting_versions = vec![existing.version.clone(), resolved_version.clone()];
                                    let requirements = vec![
                                        VersionReq::parse(&existing.version).unwrap_or_default(),
                                        VersionReq::parse(&version_req).unwrap_or_default(),
                                    ];

                                    match self.conflict_resolver.resolve_conflict(&package_name, &conflicting_versions, &requirements) {
                                        Ok(final_version) => {
                                            if final_version != existing.version {
                                                // Update with resolved version
                                                let resolved_dep = self.create_resolved_dependency(&package_name, &final_version, &metadata)?;
                                                resolved.insert(package_name.clone(), resolved_dep.clone());
                                                graph.update_node(package_name.clone(), resolved_dep);
                                            }
                                        }
                                        Err(_) => {
                                            conflicts.push(DependencyConflict {
                                                package: package_name.clone(),
                                                requested_versions: conflicting_versions,
                                                resolved_version: existing.version.clone(),
                                                reason: "Unresolvable version conflict".to_string(),
                                            });
                                        }
                                    }
                                }
                            } else {
                                // Add new dependency
                                let resolved_dep = self.create_resolved_dependency(&package_name, &resolved_version, &metadata)?;

                                // Add transitive dependencies to process
                                for (trans_name, trans_version) in &resolved_dep.dependencies {
                                    if !resolved.contains_key(trans_name) {
                                        to_process.push_back((trans_name.clone(), trans_version.clone(), depth + 1));
                                    }
                                }

                                graph.add_node(package_name.clone(), resolved_dep.clone());
                                resolved.insert(package_name, resolved_dep);
                            }
                        }
                        Err(e) => {
                            warnings.push(format!("Failed to resolve {}@{}: {}", package_name, version_req, e));
                        }
                    }
                }
                Err(e) => {
                    warnings.push(format!("Failed to fetch {}: {}", package_name, e));
                }
            }
        }

        Ok(())
    }
}

// Dependency graph for topological sorting
struct DependencyGraph {
    nodes: HashMap<String, ResolvedDependency>,
    edges: HashMap<String, Vec<String>>,
}

impl DependencyGraph {
    fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            edges: HashMap::new(),
        }
    }

    fn add_node(&mut self, name: String, dep: ResolvedDependency) {
        self.nodes.insert(name.clone(), dep.clone());

        // Add edges for dependencies
        let mut deps = Vec::new();
        for dep_name in dep.dependencies.keys() {
            deps.push(dep_name.clone());
        }
        self.edges.insert(name, deps);
    }

    fn update_node(&mut self, name: String, dep: ResolvedDependency) {
        self.add_node(name, dep);
    }

    fn topological_sort(&self) -> Result<Vec<String>> {
        let mut in_degree = HashMap::new();
        let mut graph = HashMap::new();

        // Initialize
        for name in self.nodes.keys() {
            in_degree.insert(name.clone(), 0);
            graph.insert(name.clone(), Vec::new());
        }

        // Build reverse graph and calculate in-degrees
        for (name, deps) in &self.edges {
            for dep_name in deps {
                if self.nodes.contains_key(dep_name) {
                    graph.get_mut(dep_name).unwrap().push(name.clone());
                    *in_degree.get_mut(name).unwrap() += 1;
                }
            }
        }

        // Kahn's algorithm
        let mut queue = VecDeque::new();
        let mut result = Vec::new();

        // Find nodes with no dependencies
        for (name, degree) in &in_degree {
            if *degree == 0 {
                queue.push_back(name.clone());
            }
        }

        while let Some(name) = queue.pop_front() {
            result.push(name.clone());

            if let Some(dependents) = graph.get(&name) {
                for dependent in dependents {
                    let degree = in_degree.get_mut(dependent).unwrap();
                    *degree -= 1;
                    if *degree == 0 {
                        queue.push_back(dependent.clone());
                    }
                }
            }
        }

        // Check for cycles
        if result.len() != self.nodes.len() {
            warn!("Circular dependency detected in {} packages", self.nodes.len() - result.len());
            // Add remaining packages (they form cycles)
            for name in self.nodes.keys() {
                if !result.contains(name) {
                    result.push(name.clone());
                }
            }
        }

        Ok(result)
    }
}
