use crate::errors::{NxError, Result};
use crate::registry::{Registry, PackageMetadata};
use semver::{Version, VersionReq};
use std::collections::{HashMap, HashSet, VecDeque};
use tracing::{debug, info, warn};
use indicatif::{ProgressBar, ProgressStyle};

#[derive(Debug, Clone)]
pub struct ResolvedDependency {
    pub name: String,
    pub version: String,
    pub resolved_url: String,
    pub integrity: Option<String>,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
}

#[derive(Debug)]
pub struct ResolutionResult {
    pub resolved: HashMap<String, ResolvedDependency>,
    pub install_order: Vec<String>,
    pub conflicts: Vec<DependencyConflict>,
    pub warnings: Vec<String>,
}

#[derive(Debug)]
pub struct DependencyConflict {
    pub package: String,
    pub requested_versions: Vec<String>,
    pub resolved_version: String,
    pub reason: String,
}

pub struct AdvancedResolver {
    registry: Registry,
    cache: HashMap<String, PackageMetadata>,
    resolution_cache: HashMap<String, String>,
}

impl AdvancedResolver {
    pub fn new() -> Self {
        Self {
            registry: Registry::new(),
            cache: HashMap::new(),
            resolution_cache: HashMap::new(),
        }
    }

    pub async fn resolve_dependencies(
        &mut self,
        root_deps: &HashMap<String, String>,
        dev_deps: &HashMap<String, String>,
        include_dev: bool,
    ) -> Result<ResolutionResult> {
        info!("🔍 Starting advanced dependency resolution...");
        
        let pb = ProgressBar::new_spinner();
        pb.set_style(
            ProgressStyle::default_spinner()
                .template("{spinner:.cyan} {msg}")
                .unwrap()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]),
        );
        pb.set_message("Resolving dependency tree...");
        pb.enable_steady_tick(std::time::Duration::from_millis(100));

        let mut resolved = HashMap::new();
        let mut conflicts = Vec::new();
        let mut warnings = Vec::new();
        let mut to_resolve = VecDeque::new();
        let mut visited = HashSet::new();

        // Add root dependencies
        for (name, version_req) in root_deps {
            to_resolve.push_back((name.clone(), version_req.clone(), 0, false));
        }

        // Add dev dependencies if requested
        if include_dev {
            for (name, version_req) in dev_deps {
                to_resolve.push_back((name.clone(), version_req.clone(), 0, true));
            }
        }

        let mut processed = 0;
        const MAX_DEPTH: usize = 20;
        const MAX_PACKAGES: usize = 10000;

        while let Some((package_name, version_req, depth, is_dev)) = to_resolve.pop_front() {
            processed += 1;
            
            if processed > MAX_PACKAGES {
                warn!("Reached maximum package limit, stopping resolution");
                break;
            }

            if depth > MAX_DEPTH {
                warn!("Maximum dependency depth reached for {}", package_name);
                continue;
            }

            let package_key = format!("{}@{}", package_name, version_req);
            if visited.contains(&package_key) {
                continue;
            }
            visited.insert(package_key.clone());

            pb.set_message(format!("Resolving {} (depth: {})", package_name, depth));

            // Try to resolve from cache first
            if let Some(cached_version) = self.resolution_cache.get(&package_key) {
                debug!("Using cached resolution for {}: {}", package_name, cached_version);
                continue;
            }

            // Fetch package metadata
            let metadata = match self.get_package_metadata(&package_name).await {
                Ok(meta) => meta,
                Err(e) => {
                    warnings.push(format!("Failed to fetch {}: {}", package_name, e));
                    continue;
                }
            };

            // Resolve version
            let resolved_version = match self.resolve_version(&metadata, &version_req) {
                Ok(version) => version,
                Err(e) => {
                    warnings.push(format!("Failed to resolve version for {}@{}: {}", package_name, version_req, e));
                    continue;
                }
            };

            let resolved_key = format!("{}@{}", package_name, resolved_version);
            
            // Check for conflicts
            if let Some(existing) = resolved.get(&package_name) {
                if existing.version != resolved_version {
                    conflicts.push(DependencyConflict {
                        package: package_name.clone(),
                        requested_versions: vec![existing.version.clone(), resolved_version.clone()],
                        resolved_version: existing.version.clone(),
                        reason: "Version conflict detected".to_string(),
                    });
                    continue;
                }
            }

            // Get version info
            let version_info = match metadata.versions.get(&resolved_version) {
                Some(info) => info,
                None => {
                    warnings.push(format!("Version info not found for {}@{}", package_name, resolved_version));
                    continue;
                }
            };

            // Create resolved dependency
            let resolved_dep = ResolvedDependency {
                name: package_name.clone(),
                version: resolved_version.clone(),
                resolved_url: version_info.dist.tarball.clone(),
                integrity: version_info.dist.integrity.clone(),
                dependencies: version_info.dependencies.clone().unwrap_or_default(),
                dev_dependencies: version_info.dev_dependencies.clone().unwrap_or_default(),
                peer_dependencies: version_info.peer_dependencies.clone().unwrap_or_default(),
                optional_dependencies: HashMap::new(), // TODO: Add optional deps support
            };

            // Cache the resolution
            self.resolution_cache.insert(package_key, resolved_version.clone());

            // Add child dependencies to queue
            if depth < MAX_DEPTH {
                for (dep_name, dep_version) in &resolved_dep.dependencies {
                    if dep_name != &package_name { // Avoid self-dependencies
                        to_resolve.push_back((dep_name.clone(), dep_version.clone(), depth + 1, false));
                    }
                }

                // Add peer dependencies as warnings if not satisfied
                for (peer_name, peer_version) in &resolved_dep.peer_dependencies {
                    if !resolved.contains_key(peer_name) {
                        warnings.push(format!("Peer dependency {} required by {} not found", peer_name, package_name));
                    }
                }
            }

            resolved.insert(package_name.clone(), resolved_dep);
        }

        pb.finish_with_message(format!("✅ Resolved {} packages", resolved.len()));

        // Calculate install order
        let install_order = self.calculate_install_order(&resolved)?;

        Ok(ResolutionResult {
            resolved,
            install_order,
            conflicts,
            warnings,
        })
    }

    async fn get_package_metadata(&mut self, package_name: &str) -> Result<&PackageMetadata> {
        if !self.cache.contains_key(package_name) {
            let metadata = self.registry.get_package_metadata(package_name).await?;
            self.cache.insert(package_name.to_string(), metadata);
        }
        Ok(self.cache.get(package_name).unwrap())
    }

    fn resolve_version(&self, metadata: &PackageMetadata, version_req: &str) -> Result<String> {
        // Handle dist-tags
        if let Some(version) = metadata.dist_tags.get(version_req) {
            return Ok(version.clone());
        }

        // Parse semver requirement
        let req = VersionReq::parse(version_req)?;
        
        // Get all versions and sort them
        let mut versions: Vec<Version> = metadata
            .versions
            .keys()
            .filter_map(|v| Version::parse(v).ok())
            .collect();
        
        versions.sort();
        versions.reverse(); // Prefer newer versions

        // Find the best matching version
        for version in versions {
            if req.matches(&version) {
                return Ok(version.to_string());
            }
        }

        Err(NxError::VersionNotFound {
            package: metadata.name.clone(),
            version: version_req.to_string(),
        })
    }

    fn calculate_install_order(&self, resolved: &HashMap<String, ResolvedDependency>) -> Result<Vec<String>> {
        let mut in_degree = HashMap::new();
        let mut graph = HashMap::new();

        // Initialize
        for package_name in resolved.keys() {
            in_degree.insert(package_name.clone(), 0);
            graph.insert(package_name.clone(), Vec::new());
        }

        // Build dependency graph
        for (package_name, dep) in resolved {
            for dep_name in dep.dependencies.keys() {
                if resolved.contains_key(dep_name) {
                    graph.get_mut(dep_name).unwrap().push(package_name.clone());
                    *in_degree.get_mut(package_name).unwrap() += 1;
                }
            }
        }

        // Topological sort
        let mut queue = VecDeque::new();
        let mut result = Vec::new();

        // Find nodes with no dependencies
        for (package_name, degree) in &in_degree {
            if *degree == 0 {
                queue.push_back(package_name.clone());
            }
        }

        while let Some(package_name) = queue.pop_front() {
            result.push(package_name.clone());

            if let Some(dependents) = graph.get(&package_name) {
                for dependent in dependents {
                    let degree = in_degree.get_mut(dependent).unwrap();
                    *degree -= 1;
                    if *degree == 0 {
                        queue.push_back(dependent.clone());
                    }
                }
            }
        }

        // Check for cycles
        if result.len() != resolved.len() {
            warn!("Circular dependency detected, using partial order");
        }

        Ok(result)
    }
}
