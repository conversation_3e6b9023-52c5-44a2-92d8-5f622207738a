use crate::cache::Cache;
use crate::errors::{NxError, Result};
use crate::linker::ModuleLinker;
use crate::lockfile::{LockFile, LockFileManager};
use crate::manifest::{ManifestManager, PackageManifest};
use crate::registry::{Registry, VersionInfo};
use crate::resolver::DependencyResolver;
use crate::advanced_resolver::AdvancedResolver;
use crate::modern_ui::ModernInstallUI;
use crate::performance::PerformanceOptimizer;
use indicatif::{ProgressBar, ProgressStyle};
use std::collections::HashMap;
use std::path::PathBuf;
use tracing::{info, warn};
use crossterm::style::Stylize;

pub struct PackageInstaller {
    registry: Registry,
    cache: Cache,
    resolver: DependencyResolver,
    advanced_resolver: AdvancedResolver,
    linker: ModuleLinker,
    performance_optimizer: PerformanceOptimizer,
}

impl PackageInstaller {
    pub fn new() -> Result<Self> {
        let project_root = std::env::current_dir()?;
        Ok(Self {
            registry: Registry::new(),
            cache: Cache::new()?,
            resolver: DependencyResolver::new(),
            advanced_resolver: AdvancedResolver::new(),
            linker: ModuleLinker::new(project_root)?,
            performance_optimizer: PerformanceOptimizer::new(),
        })
    }

    pub async fn install_package(
        &self,
        package_spec: &str,
        _is_dev: bool,
        _is_global: bool,
    ) -> Result<()> {
        let (package_name, version_req) = self.parse_package_spec(package_spec)?;
        
        info!("Installing package: {}@{}", package_name, version_req);

        // Check if already cached
        if self.cache.is_package_cached(&package_name, &version_req) {
            info!("Package {}@{} already cached", package_name, version_req);
            return Ok(());
        }

        // Fetch package metadata
        let metadata = self.registry.get_package_metadata(&package_name).await?;
        
        // Resolve version
        let resolved_version = self.registry.resolve_version(&metadata, &version_req)?;
        
        let version_info = metadata
            .versions
            .get(&resolved_version)
            .ok_or_else(|| NxError::VersionNotFound {
                package: package_name.clone(),
                version: resolved_version.clone(),
            })?;

        // Download and cache package
        self.download_and_cache_package(&package_name, &resolved_version, version_info)
            .await?;

        // Create symlinks in .nx_modules
        self.linker.link_package(&package_name, &resolved_version).await?;

        // Setup bin links
        self.linker.setup_bin_links(&package_name, &resolved_version)?;

        info!("Successfully installed {}@{}", package_name, resolved_version);
        Ok(())
    }

    async fn download_and_cache_package(
        &self,
        package_name: &str,
        version: &str,
        version_info: &VersionInfo,
    ) -> Result<PathBuf> {
        let pb = ProgressBar::new_spinner();
        pb.set_style(
            ProgressStyle::default_spinner()
                .template("{spinner:.cyan} {msg}")
                .unwrap()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]),
        );

        // Show package size if available
        let size_info = if let Some(size) = version_info.dist.unpacked_size {
            format!(" ({:.1} KB)", size as f64 / 1024.0)
        } else {
            String::new()
        };

        pb.set_message(format!("📦 Downloading {}@{}{}", package_name, version, size_info));
        pb.enable_steady_tick(std::time::Duration::from_millis(100));

        let tarball_data = self
            .registry
            .download_tarball(&version_info.dist.tarball)
            .await?;

        pb.set_message(format!("💾 Caching {}@{}", package_name, version));

        let cache_dir = self
            .cache
            .cache_package(package_name, version, &tarball_data, version_info)
            .await?;

        pb.finish_with_message(format!("✅ {}@{} ({:.1} KB)",
            package_name,
            version,
            tarball_data.len() as f64 / 1024.0
        ));
        Ok(cache_dir)
    }

    pub async fn install_from_manifest(&self, _is_global: bool) -> Result<()> {
        // Find and load manifest
        let manifest_path = ManifestManager::find_manifest()
            .ok_or_else(|| NxError::ManifestError {
                message: "No package.json or package.toml found".to_string(),
            })?;

        let manifest = ManifestManager::load_manifest(&manifest_path)?;

        // Get all dependencies
        let all_deps = ManifestManager::get_all_dependencies(&manifest);

        if all_deps.is_empty() {
            info!("No dependencies to install");
            return Ok(());
        }

        info!("Installing {} dependencies", all_deps.len());

        // Resolve full dependency tree
        info!("Resolving dependency tree...");
        let resolution_graph = match self.resolver.resolve_dependencies(&all_deps, true).await {
            Ok(graph) => {
                info!("Resolved {} packages in dependency tree", graph.packages.len());
                graph
            }
            Err(e) => {
                warn!("Dependency resolution failed: {}, falling back to direct install", e);
                // Fallback to direct installation
                for (name, version) in &all_deps {
                    let spec = format!("{}@{}", name, version);
                    self.install_package(&spec, false, false).await?;
                }

                let lockfile = LockFileManager::create_from_resolved_packages(
                    &std::collections::HashMap::new(),
                    &manifest.dependencies.unwrap_or_default(),
                    &manifest.dev_dependencies.unwrap_or_default(),
                );
                let lockfile_path = std::env::current_dir()?.join("nx-lock.json");
                LockFileManager::save_lockfile(&lockfile_path, &lockfile)?;
                info!("Generated nx-lock.json (fallback mode)");
                return Ok(());
            }
        };

        // Install packages in dependency order with progress tracking
        let total_packages = resolution_graph.install_order.len();
        let main_pb = ProgressBar::new(total_packages as u64);
        main_pb.set_style(
            ProgressStyle::default_bar()
                .template("🚀 Installing packages [{bar:40.cyan/blue}] {pos}/{len} {msg}")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏  "),
        );

        let mut installed_count = 0;
        let mut failed_packages = Vec::new();

        for package_id in &resolution_graph.install_order {
            if let Some(resolved_package) = resolution_graph.packages.get(package_id) {
                let spec = format!("{}@{}", resolved_package.name, resolved_package.version);
                main_pb.set_message(format!("Installing {}", spec));

                match self.install_package(&spec, false, false).await {
                    Ok(_) => {
                        installed_count += 1;
                        main_pb.inc(1);
                    }
                    Err(e) => {
                        warn!("Failed to install {}: {}", spec, e);
                        failed_packages.push(spec);
                        main_pb.inc(1);
                    }
                }
            }
        }

        main_pb.finish_with_message(format!(
            "✅ Installed {}/{} packages{}",
            installed_count,
            total_packages,
            if failed_packages.is_empty() {
                String::new()
            } else {
                format!(" ({} failed)", failed_packages.len())
            }
        ));

        if !failed_packages.is_empty() {
            warn!("Failed to install: {}", failed_packages.join(", "));
        }

        // Generate lockfile with resolved packages
        let lockfile = LockFileManager::create_from_resolved_packages(
            &resolution_graph.packages,
            &manifest.dependencies.unwrap_or_default(),
            &manifest.dev_dependencies.unwrap_or_default(),
        );

        let lockfile_path = std::env::current_dir()?.join("nx-lock.json");
        LockFileManager::save_lockfile(&lockfile_path, &lockfile)?;

        info!("Generated nx-lock.json");
        Ok(())
    }

    pub fn parse_package_spec(&self, spec: &str) -> Result<(String, String)> {
        if let Some(at_pos) = spec.rfind('@') {
            if at_pos > 0 {
                // Handle scoped packages like @types/node@1.0.0
                let package_name = spec[..at_pos].to_string();
                let version = spec[at_pos + 1..].to_string();
                return Ok((package_name, version));
            }
        }
        
        // No version specified, use "latest"
        Ok((spec.to_string(), "latest".to_string()))
    }

    pub async fn install_from_manifest_modern(&self) -> Result<()> {
        // Find and load manifest
        let manifest_path = ManifestManager::find_manifest()
            .ok_or_else(|| NxError::ManifestError {
                message: "No package.json or package.toml found".to_string(),
            })?;

        let manifest = ManifestManager::load_manifest(&manifest_path)?;

        // Get all dependencies
        let all_deps = ManifestManager::get_all_dependencies(&manifest);

        if all_deps.is_empty() {
            println!("{}", "No dependencies to install".dim());
            return Ok(());
        }

        // Create modern UI
        let ui = ModernInstallUI::new(all_deps.len());
        ui.show_clean_header();
        ui.set_phase("Resolving dependencies");

        // Use advanced resolver for better conflict handling
        let resolution_result = self.advanced_resolver
            .resolve_dependencies_parallel(
                &manifest.dependencies.unwrap_or_default(),
                &manifest.dev_dependencies.unwrap_or_default(),
                true,
            )
            .await?;

        let total_packages = resolution_result.resolved.len();
        ui.set_phase(&format!("Installing {} packages", total_packages));

        // Prepare download list
        let download_list: Vec<(String, String)> = resolution_result.resolved
            .values()
            .map(|dep| (dep.name.clone(), dep.resolved_url.clone()))
            .collect();

        // Use performance optimizer for parallel downloads
        let downloaded_packages = self.performance_optimizer
            .download_packages_parallel(
                download_list,
                |_name, _progress| {
                    // Minimal progress callback to avoid UI clutter
                },
            )
            .await?;

        ui.set_phase("Extracting packages");

        // Extract packages in parallel
        let cache_dir = self.cache.get_cache_dir();
        let _extracted_paths = self.performance_optimizer
            .extract_packages_parallel(downloaded_packages, &cache_dir)
            .await?;

        ui.set_phase("Linking modules");

        // Link modules
        self.linker.link_all_modules().await?;

        // Generate lockfile
        let lockfile = LockFileManager::create_from_resolved_packages(
            &resolution_result.resolved,
            &manifest.dependencies.unwrap_or_default(),
            &manifest.dev_dependencies.unwrap_or_default(),
        );
        let lockfile_path = std::env::current_dir()?.join("nx-lock.json");
        LockFileManager::save_lockfile(&lockfile_path, &lockfile)?;

        ui.finish_installation();
        Ok(())
    }
}

pub async fn handle_install(
    packages: Vec<String>,
    dev: bool,
    save_dev: bool,
    global: bool,
) -> Result<()> {
    let installer = PackageInstaller::new()?;
    let is_dev = dev || save_dev;

    if packages.is_empty() {
        // Install from package.json using modern method
        installer.install_from_manifest_modern().await?;
        return Ok(());
    }

    // Install specific packages and update manifest
    for package in packages {
        installer.install_package(&package, is_dev, global).await?;

        // Update package.json if it exists
        if let Some(manifest_path) = ManifestManager::find_manifest() {
            let mut manifest = ManifestManager::load_manifest(&manifest_path)?;
            let (name, version) = installer.parse_package_spec(&package)?;
            ManifestManager::add_dependency(&mut manifest, name, version, is_dev);
            ManifestManager::save_manifest(&manifest_path, &manifest)?;
            info!("Updated manifest with new dependency");
        }
    }

    Ok(())
}

pub async fn handle_uninstall(packages: Vec<String>, global: bool) -> Result<()> {
    info!("Uninstalling packages: {:?} (global: {})", packages, global);

    if packages.is_empty() {
        return Err(NxError::ManifestError {
            message: "No packages specified for uninstall".to_string(),
        });
    }

    let installer = PackageInstaller::new()?;

    for package_name in packages {
        // Remove from .nx_modules
        installer.linker.unlink_package(&package_name).await?;

        // Update package.json if it exists
        if let Some(manifest_path) = ManifestManager::find_manifest() {
            let mut manifest = ManifestManager::load_manifest(&manifest_path)?;
            let removed = ManifestManager::remove_dependency(&mut manifest, &package_name);

            if removed {
                ManifestManager::save_manifest(&manifest_path, &manifest)?;
                info!("Removed {} from manifest", package_name);
            } else {
                warn!("Package {} not found in manifest", package_name);
            }
        }

        info!("Successfully uninstalled {}", package_name);
    }

    Ok(())
}

pub async fn handle_run(script: String, args: Vec<String>) -> Result<()> {
    info!("Running script: {} with args: {:?}", script, args);

    // Find and load manifest
    let manifest_path = ManifestManager::find_manifest()
        .ok_or_else(|| NxError::ManifestError {
            message: "No package.json or package.toml found".to_string(),
        })?;

    let manifest = ManifestManager::load_manifest(&manifest_path)?;

    // Get script command
    let script_command = manifest
        .scripts
        .as_ref()
        .and_then(|scripts| scripts.get(&script))
        .ok_or_else(|| NxError::ManifestError {
            message: format!("Script '{}' not found in package.json", script),
        })?;

    // Prepare environment
    let mut cmd = std::process::Command::new("cmd");
    cmd.args(&["/C", script_command]);

    // Add .nx_modules/.bin to PATH
    let current_dir = std::env::current_dir()?;
    let bin_dir = current_dir.join(".nx_modules").join(".bin");
    if bin_dir.exists() {
        let mut path = std::env::var("PATH").unwrap_or_default();
        path = format!("{};{}", bin_dir.to_string_lossy(), path);
        cmd.env("PATH", path);
    }

    // Add script arguments
    for arg in args {
        cmd.arg(&arg);
    }

    cmd.current_dir(&current_dir);

    info!("Executing: {}", script_command);

    // Execute the script
    let status = cmd.status()?;

    if !status.success() {
        return Err(NxError::ManifestError {
            message: format!("Script '{}' failed with exit code: {:?}", script, status.code()),
        });
    }

    info!("Script '{}' completed successfully", script);
    Ok(())
}

pub async fn handle_publish(registry: Option<String>) -> Result<()> {
    let registry_url = registry.unwrap_or_else(|| "https://registry.npmjs.org".to_string());
    info!("Publishing package to registry: {}", registry_url);

    // Find and load manifest
    let manifest_path = ManifestManager::find_manifest()
        .ok_or_else(|| NxError::ManifestError {
            message: "No package.json or package.toml found".to_string(),
        })?;

    let manifest = ManifestManager::load_manifest(&manifest_path)?;

    // Validate manifest for publishing
    if manifest.name.is_empty() {
        return Err(NxError::ManifestError {
            message: "Package name is required for publishing".to_string(),
        });
    }

    if manifest.version.is_empty() {
        return Err(NxError::ManifestError {
            message: "Package version is required for publishing".to_string(),
        });
    }

    // Check if package files exist
    let current_dir = std::env::current_dir()?;
    let main_file = manifest.main.as_deref().unwrap_or("index.js");
    let main_path = current_dir.join(main_file);

    if !main_path.exists() {
        warn!("Main file {} does not exist", main_file);
    }

    // Create tarball (simplified - in real implementation would use tar crate)
    info!("Creating package tarball for {}@{}", manifest.name, manifest.version);

    // In a real implementation, we would:
    // 1. Create a tarball of the package files
    // 2. Calculate integrity hash
    // 3. Upload to registry with authentication
    // 4. Handle registry response

    info!("Package {}@{} would be published to {}", manifest.name, manifest.version, registry_url);
    warn!("Actual publishing to registry not implemented - this is a simulation");

    Ok(())
}

pub async fn handle_link(package: Option<String>) -> Result<()> {
    let package_path = package.unwrap_or_else(|| ".".to_string());
    info!("Linking package from: {}", package_path);

    let package_dir = std::path::Path::new(&package_path);
    if !package_dir.exists() {
        return Err(NxError::ManifestError {
            message: format!("Package directory not found: {}", package_path),
        });
    }

    // Find package.json in the target directory
    let manifest_path = package_dir.join("package.json");
    if !manifest_path.exists() {
        return Err(NxError::ManifestError {
            message: format!("No package.json found in: {}", package_path),
        });
    }

    let manifest = ManifestManager::load_manifest(&manifest_path)?;
    let package_name = &manifest.name;

    // Create symlink in .nx_modules
    let current_dir = std::env::current_dir()?;
    let linker = ModuleLinker::new(current_dir)?;
    let nx_modules_dir = linker.get_nx_modules_dir();
    std::fs::create_dir_all(&nx_modules_dir)?;

    let link_target = nx_modules_dir.join(package_name);
    let absolute_package_dir = package_dir.canonicalize()?;

    // Remove existing link if it exists
    if link_target.exists() {
        if link_target.is_symlink() {
            std::fs::remove_file(&link_target)?;
        } else {
            std::fs::remove_dir_all(&link_target)?;
        }
    }

    // Create symlink
    #[cfg(windows)]
    std::os::windows::fs::symlink_dir(&absolute_package_dir, &link_target)?;

    #[cfg(unix)]
    std::os::unix::fs::symlink(&absolute_package_dir, &link_target)?;

    info!("Successfully linked {} from {}", package_name, package_path);
    Ok(())
}
