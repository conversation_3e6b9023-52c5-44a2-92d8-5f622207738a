#!/usr/bin/env node

const { getBinaryName } = require('./install.js');
const path = require('path');
const fs = require('fs');
const https = require('https');
const os = require('os');

const GITHUB_RELEASES_URL = 'https://api.github.com/repos/justm3sunny/nx/releases/latest';
const BINARY_BASE_URL = 'https://github.com/justm3sunny/nx/releases/download';

async function downloadBinary() {
  console.log('🚀 Setting up nx package manager...');
  
  const binaryName = getBinaryName();
  const binaryDir = path.join(__dirname, 'bin');
  const binaryPath = path.join(binaryDir, binaryName);
  
  // Create bin directory if it doesn't exist
  if (!fs.existsSync(binaryDir)) {
    fs.mkdirSync(binaryDir, { recursive: true });
  }
  
  // Skip download if binary already exists
  if (fs.existsSync(binaryPath)) {
    console.log('✅ nx binary already exists');
    return;
  }
  
  try {
    // Get latest release info
    console.log('📡 Fetching latest release information...');
    const releaseInfo = await fetchReleaseInfo();
    const version = releaseInfo.tag_name;
    
    // Download binary
    console.log(`📦 Downloading nx ${version} for ${os.platform()}-${os.arch()}...`);
    const downloadUrl = `${BINARY_BASE_URL}/${version}/${binaryName}`;
    
    await downloadFile(downloadUrl, binaryPath);
    
    // Make executable on Unix systems
    if (os.platform() !== 'win32') {
      fs.chmodSync(binaryPath, 0o755);
    }
    
    console.log('✅ nx package manager installed successfully!');
    console.log('');
    console.log('🎉 You can now use nx:');
    console.log('   nx --help');
    console.log('   nx install <package>');
    console.log('   nx run <script>');
    console.log('');
    
  } catch (error) {
    console.error('❌ Failed to install nx:', error.message);
    console.error('');
    console.error('Please try:');
    console.error('1. Check your internet connection');
    console.error('2. Report this issue at: https://github.com/justm3sunny/nx/issues');
    process.exit(1);
  }
}

function fetchReleaseInfo() {
  return new Promise((resolve, reject) => {
    const options = {
      headers: {
        'User-Agent': 'nx-package-manager-installer'
      }
    };
    
    https.get(GITHUB_RELEASES_URL, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const releaseInfo = JSON.parse(data);
          resolve(releaseInfo);
        } catch (err) {
          reject(new Error('Failed to parse release information'));
        }
      });
    }).on('error', (err) => {
      reject(new Error(`Failed to fetch release information: ${err.message}`));
    });
  });
}

function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(destination);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Download failed with status ${response.statusCode}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize) {
          const progress = Math.round((downloadedSize / totalSize) * 100);
          process.stdout.write(`\r   Progress: ${progress}%`);
        }
      });
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log('\n   Download completed!');
        resolve();
      });
      
    }).on('error', (err) => {
      fs.unlink(destination, () => {}); // Delete partial file
      reject(new Error(`Download failed: ${err.message}`));
    });
  });
}

if (require.main === module) {
  downloadBinary().catch((err) => {
    console.error('Installation failed:', err.message);
    process.exit(1);
  });
}
