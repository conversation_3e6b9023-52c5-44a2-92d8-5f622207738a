use crate::errors::{NxError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use tracing::{debug, info, warn};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BundlerConfig {
    pub framework: Framework,
    pub build_target: BuildTarget,
    pub output_format: OutputFormat,
    pub mode: BuildMode,
    pub entry_points: Vec<String>,
    pub output_dir: String,
    pub source_dir: String,
    pub public_dir: Option<String>,
    pub features: BundlerFeatures,
    pub plugins: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Framework {
    React,
    Vue,
    Svelte,
    Angular,
    Solid,
    Preact,
    Vanilla,
    NodeJs,
    Express,
    NextJs,
    Astro,
    Nuxt,
    SvelteKit,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BuildTarget {
    Web,
    SSR,
    CLI,
    Hybrid,
    Library,
    WebWorker,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum OutputFormat {
    ESM,
    CJS,
    IIFE,
    UMD,
    SystemJS,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BuildMode {
    Development,
    Production,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BundlerFeatures {
    pub tree_shaking: bool,
    pub minification: bool,
    pub code_splitting: bool,
    pub polyfills: bool,
    pub hot_reload: bool,
    pub typescript: bool,
    pub css_preprocessing: CssPreprocessing,
    pub html_inlining: bool,
    pub source_maps: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CssPreprocessing {
    None,
    Sass,
    Less,
    Stylus,
    PostCSS,
    Tailwind,
}

impl Default for BundlerConfig {
    fn default() -> Self {
        Self {
            framework: Framework::Vanilla,
            build_target: BuildTarget::Web,
            output_format: OutputFormat::ESM,
            mode: BuildMode::Development,
            entry_points: vec!["src/index.js".to_string()],
            output_dir: "dist".to_string(),
            source_dir: "src".to_string(),
            public_dir: Some("public".to_string()),
            features: BundlerFeatures {
                tree_shaking: true,
                minification: false,
                code_splitting: true,
                polyfills: true,
                hot_reload: true,
                typescript: false,
                css_preprocessing: CssPreprocessing::None,
                html_inlining: false,
                source_maps: true,
            },
            plugins: Vec::new(),
        }
    }
}

pub struct ProjectDetector;

impl ProjectDetector {
    pub fn detect_framework(project_dir: &Path) -> Result<Framework> {
        let package_json_path = project_dir.join("package.json");
        
        if package_json_path.exists() {
            let content = fs::read_to_string(&package_json_path)?;
            let package_json: serde_json::Value = serde_json::from_str(&content)?;
            
            // Check dependencies for framework detection
            if let Some(deps) = package_json.get("dependencies").and_then(|d| d.as_object()) {
                if deps.contains_key("react") {
                    return Ok(Framework::React);
                }
                if deps.contains_key("vue") {
                    return Ok(Framework::Vue);
                }
                if deps.contains_key("svelte") {
                    return Ok(Framework::Svelte);
                }
                if deps.contains_key("@angular/core") {
                    return Ok(Framework::Angular);
                }
                if deps.contains_key("solid-js") {
                    return Ok(Framework::Solid);
                }
                if deps.contains_key("preact") {
                    return Ok(Framework::Preact);
                }
                if deps.contains_key("next") {
                    return Ok(Framework::NextJs);
                }
                if deps.contains_key("astro") {
                    return Ok(Framework::Astro);
                }
                if deps.contains_key("nuxt") {
                    return Ok(Framework::Nuxt);
                }
                if deps.contains_key("@sveltejs/kit") {
                    return Ok(Framework::SvelteKit);
                }
                if deps.contains_key("express") {
                    return Ok(Framework::Express);
                }
            }
        }
        
        // Check for framework-specific files
        if project_dir.join("next.config.js").exists() || project_dir.join("next.config.ts").exists() {
            return Ok(Framework::NextJs);
        }
        
        if project_dir.join("astro.config.js").exists() || project_dir.join("astro.config.ts").exists() {
            return Ok(Framework::Astro);
        }
        
        if project_dir.join("svelte.config.js").exists() {
            return Ok(Framework::SvelteKit);
        }
        
        if project_dir.join("nuxt.config.js").exists() || project_dir.join("nuxt.config.ts").exists() {
            return Ok(Framework::Nuxt);
        }
        
        // Check for TypeScript
        if project_dir.join("tsconfig.json").exists() {
            // Default to vanilla with TypeScript
            return Ok(Framework::Vanilla);
        }
        
        Ok(Framework::Vanilla)
    }
    
    pub fn detect_entry_points(project_dir: &Path) -> Vec<String> {
        let mut entry_points = Vec::new();
        
        // Common entry point patterns
        let candidates = vec![
            "src/index.js",
            "src/index.ts",
            "src/main.js",
            "src/main.ts",
            "src/app.js",
            "src/app.ts",
            "index.js",
            "index.ts",
            "main.js",
            "main.ts",
        ];
        
        for candidate in candidates {
            if project_dir.join(candidate).exists() {
                entry_points.push(candidate.to_string());
                break; // Use first found entry point
            }
        }
        
        if entry_points.is_empty() {
            entry_points.push("src/index.js".to_string()); // Default
        }
        
        entry_points
    }
    
    pub fn detect_typescript(project_dir: &Path) -> bool {
        project_dir.join("tsconfig.json").exists()
    }
    
    pub fn detect_css_preprocessing(project_dir: &Path) -> CssPreprocessing {
        if project_dir.join("tailwind.config.js").exists() || 
           project_dir.join("tailwind.config.ts").exists() {
            return CssPreprocessing::Tailwind;
        }
        
        if project_dir.join("postcss.config.js").exists() {
            return CssPreprocessing::PostCSS;
        }
        
        // Check for sass/scss files
        if let Ok(entries) = fs::read_dir(project_dir.join("src")) {
            for entry in entries.flatten() {
                if let Some(ext) = entry.path().extension() {
                    match ext.to_str() {
                        Some("scss") | Some("sass") => return CssPreprocessing::Sass,
                        Some("less") => return CssPreprocessing::Less,
                        Some("styl") => return CssPreprocessing::Stylus,
                        _ => {}
                    }
                }
            }
        }
        
        CssPreprocessing::None
    }
}

pub struct Bundler {
    config: BundlerConfig,
    project_dir: PathBuf,
}

impl Bundler {
    pub fn new(project_dir: PathBuf) -> Self {
        Self {
            config: BundlerConfig::default(),
            project_dir,
        }
    }

    pub fn with_config(project_dir: PathBuf, config: BundlerConfig) -> Self {
        Self {
            config,
            project_dir,
        }
    }

    pub fn load_config(&mut self) -> Result<()> {
        // Try to load nx.config.json
        let config_path = self.project_dir.join("nx.config.json");
        if config_path.exists() {
            let content = fs::read_to_string(&config_path)?;
            self.config = serde_json::from_str(&content)?;
            info!("Loaded configuration from nx.config.json");
            return Ok(());
        }

        // Try to load nx.config.js (simplified - would need JS execution in real implementation)
        let js_config_path = self.project_dir.join("nx.config.js");
        if js_config_path.exists() {
            warn!("nx.config.js found but JS config loading not yet implemented");
        }

        Ok(())
    }

    pub fn save_config(&self) -> Result<()> {
        let config_path = self.project_dir.join("nx.config.json");
        let content = serde_json::to_string_pretty(&self.config)?;
        fs::write(&config_path, content)?;
        info!("Saved configuration to nx.config.json");
        Ok(())
    }
    
    pub fn auto_detect(&mut self) -> Result<()> {
        info!("🔍 Auto-detecting project configuration...");
        
        self.config.framework = ProjectDetector::detect_framework(&self.project_dir)?;
        self.config.entry_points = ProjectDetector::detect_entry_points(&self.project_dir);
        self.config.features.typescript = ProjectDetector::detect_typescript(&self.project_dir);
        self.config.features.css_preprocessing = ProjectDetector::detect_css_preprocessing(&self.project_dir);
        
        info!("Detected framework: {:?}", self.config.framework);
        info!("Entry points: {:?}", self.config.entry_points);
        info!("TypeScript: {}", self.config.features.typescript);
        info!("CSS preprocessing: {:?}", self.config.features.css_preprocessing);
        
        Ok(())
    }
    
    pub async fn build(&self) -> Result<BuildResult> {
        info!("🚀 Starting build process...");
        
        // Create output directory
        let output_path = self.project_dir.join(&self.config.output_dir);
        fs::create_dir_all(&output_path)?;
        
        // Simulate build process (in real implementation, this would use SWC/esbuild)
        let start_time = std::time::Instant::now();
        
        info!("📦 Bundling {} entry points...", self.config.entry_points.len());
        
        // Simulate processing each entry point
        let mut output_files = Vec::new();
        for entry_point in &self.config.entry_points {
            let output_file = self.process_entry_point(entry_point).await?;
            output_files.push(output_file);
        }
        
        let build_time = start_time.elapsed();
        
        let result = BuildResult {
            output_files,
            build_time,
            total_size: 0, // Would be calculated from actual files
            gzipped_size: 0,
        };
        
        info!("✅ Build completed in {:?}", build_time);
        
        Ok(result)
    }
    
    async fn process_entry_point(&self, entry_point: &str) -> Result<OutputFile> {
        debug!("Processing entry point: {}", entry_point);
        
        // In a real implementation, this would:
        // 1. Parse the file with SWC
        // 2. Resolve dependencies
        // 3. Apply transformations
        // 4. Bundle and optimize
        // 5. Write output
        
        let output_name = format!("{}.js", 
            Path::new(entry_point)
                .file_stem()
                .unwrap_or_default()
                .to_string_lossy()
        );
        
        Ok(OutputFile {
            name: output_name,
            size: 1024, // Placeholder
            gzipped_size: 512, // Placeholder
        })
    }
}

#[derive(Debug)]
pub struct BuildResult {
    pub output_files: Vec<OutputFile>,
    pub build_time: std::time::Duration,
    pub total_size: u64,
    pub gzipped_size: u64,
}

#[derive(Debug)]
pub struct OutputFile {
    pub name: String,
    pub size: u64,
    pub gzipped_size: u64,
}

pub async fn handle_build(mode: String, watch: bool, output: Option<String>) -> Result<()> {
    let current_dir = std::env::current_dir()?;
    let mut bundler = Bundler::new(current_dir.clone());

    // Auto-detect project configuration
    bundler.auto_detect()?;

    // Override configuration based on CLI arguments
    bundler.config.mode = match mode.as_str() {
        "prod" | "production" => BuildMode::Production,
        _ => BuildMode::Development,
    };

    if let Some(output_dir) = output {
        bundler.config.output_dir = output_dir;
    }

    // Enable production optimizations
    if matches!(bundler.config.mode, BuildMode::Production) {
        bundler.config.features.minification = true;
        bundler.config.features.tree_shaking = true;
        bundler.config.features.source_maps = false;
    }

    info!("🔧 Build Configuration:");
    info!("  Framework: {:?}", bundler.config.framework);
    info!("  Mode: {:?}", bundler.config.mode);
    info!("  Output: {}", bundler.config.output_dir);
    info!("  Entry points: {:?}", bundler.config.entry_points);

    if watch {
        info!("👀 Watch mode enabled - watching for changes...");
        // In a real implementation, this would set up file watchers
        warn!("Watch mode not yet fully implemented");
    }

    // Build the project
    let result = bundler.build().await?;

    // Display build results
    println!("\n🎉 Build completed successfully!");
    println!("📊 Build Statistics:");
    println!("  ⏱️  Build time: {:?}", result.build_time);
    println!("  📦 Output files: {}", result.output_files.len());

    for file in &result.output_files {
        println!("    📄 {} ({:.1} KB)", file.name, file.size as f64 / 1024.0);
    }

    println!("  📁 Output directory: {}", bundler.config.output_dir);

    if matches!(bundler.config.mode, BuildMode::Production) {
        println!("  ✨ Production optimizations applied");
    }

    Ok(())
}
