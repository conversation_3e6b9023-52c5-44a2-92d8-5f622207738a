{"name": "nx-package-manager", "version": "0.1.0", "description": "Ultra-fast npm package manager written in Rust - faster than npm, yarn, and pnpm", "main": "install.js", "bin": {"nx": "./install.js"}, "scripts": {"postinstall": "node postinstall.js", "test": "echo \"Error: no test specified\" && exit 1"}, "files": ["install.js", "postinstall.js", "bin/", "README.md"], "keywords": ["package-manager", "npm", "yarn", "pnpm", "rust", "fast", "performance", "cli", "javascript", "node"], "author": "NX Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/justm3sunny/nx.git"}, "homepage": "https://github.com/justm3sunny/nx#readme", "bugs": {"url": "https://github.com/justm3sunny/nx/issues"}, "engines": {"node": ">=14.0.0"}, "os": ["win32", "darwin", "linux"], "cpu": ["x64", "arm64"]}