use anyhow::Result;
use clap::Parser;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

mod cli;
mod cache;
mod install;
mod resolver;
mod advanced_resolver;
mod manifest;
mod registry;
mod lockfile;
mod linker;
mod bundler;
mod modern_ui;
mod utils;
mod errors;

use cli::{Cli, Commands};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "nx=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    let cli = Cli::parse();

    match cli.command {
        Commands::Install { packages, dev, save_dev, global } => {
            install::handle_install(packages, dev, save_dev, global).await?;
        }
        Commands::Uninstall { packages, global } => {
            install::handle_uninstall(packages, global).await?;
        }
        Commands::Run { script, args } => {
            install::handle_run(script, args).await?;
        }
        Commands::Init { name } => {
            manifest::handle_init(name).await?;
        }
        Commands::Cache { action } => {
            cache::handle_cache(action).await?;
        }
        Commands::Publish { registry } => {
            install::handle_publish(registry).await?;
        }
        Commands::Link { package } => {
            install::handle_link(package).await?;
        }
        Commands::Config { key, value } => {
            utils::handle_config(key, value).await?;
        }
        Commands::Bench => {
            utils::handle_bench().await?;
        }
        Commands::Build { mode, watch, output } => {
            bundler::handle_build(mode, watch, output).await?;
        }
        Commands::Build { mode, watch, output } => {
            bundler::handle_build(mode, watch, output).await?;
        }
        Commands::Build { mode, watch, output } => {
            bundler::handle_build(mode, watch, output).await?;
        }
    }

    Ok(())
}
