use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use reqwest::Client;
use dashmap::DashMap;
use rayon::prelude::*;
use parking_lot::Mutex;
use bytes::Bytes;
use sha2::{Sha256, Digest};
use memmap2::Mmap;
use std::fs::File;
use std::io::{Write, BufWriter};
use futures::stream::{self, StreamExt};
use tokio::io::AsyncWriteExt;

#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    pub start_time: Instant,
    pub download_speed: f64,
    pub cache_hit_rate: f64,
    pub memory_usage: u64,
    pub cpu_usage: f32,
    pub parallel_downloads: usize,
    pub total_packages: usize,
    pub processed_packages: usize,
}

pub struct PerformanceOptimizer {
    metrics: Arc<RwLock<PerformanceMetrics>>,
    http_client: Client,
    download_cache: Arc<DashMap<String, CachedDownload>>,
    max_concurrent_downloads: usize,
    connection_pool: ConnectionPool,
}

#[derive(Debug, Clone)]
struct CachedDownload {
    data: Bytes,
    integrity: String,
    timestamp: Instant,
    size: u64,
}

struct ConnectionPool {
    client: Client,
    active_connections: Arc<Mutex<usize>>,
    max_connections: usize,
}

impl ConnectionPool {
    fn new(max_connections: usize) -> Self {
        let client = Client::builder()
            .pool_max_idle_per_host(max_connections / 2)
            .pool_idle_timeout(Duration::from_secs(30))
            .timeout(Duration::from_secs(30))
            .tcp_keepalive(Duration::from_secs(60))
            .http2_prior_knowledge()
            .gzip(true)
            .brotli(true)
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            active_connections: Arc::new(Mutex::new(0)),
            max_connections,
        }
    }

    async fn download_with_retry(&self, url: &str, max_retries: usize) -> Result<Bytes, crate::errors::NxError> {
        let mut last_error = None;

        for attempt in 0..=max_retries {
            // Wait for available connection slot
            loop {
                let active = *self.active_connections.lock();
                if active < self.max_connections {
                    *self.active_connections.lock() += 1;
                    break;
                }
                tokio::time::sleep(Duration::from_millis(10)).await;
            }

            let result = self.client.get(url).send().await;

            match result {
                Ok(response) if response.status().is_success() => {
                    let bytes = response.bytes().await;
                    *self.active_connections.lock() -= 1;

                    match bytes {
                        Ok(data) => return Ok(data),
                        Err(e) => last_error = Some(crate::errors::NxError::HttpError(e)),
                    }
                }
                Ok(response) => {
                    *self.active_connections.lock() -= 1;
                    last_error = Some(crate::errors::NxError::RegistryError {
                        message: format!("HTTP {}: {}", response.status(), url),
                    });
                }
                Err(e) => {
                    *self.active_connections.lock() -= 1;
                    last_error = Some(crate::errors::NxError::HttpError(e));
                }
            }

            if attempt < max_retries {
                let delay = Duration::from_millis(100 * (1 << attempt)); // Exponential backoff
                tokio::time::sleep(delay).await;
                debug!("Retrying download {} (attempt {}/{})", url, attempt + 1, max_retries + 1);
            }
        }

        Err(last_error.unwrap())
    }
}

impl PerformanceOptimizer {
    pub fn new() -> Self {
        let max_concurrent = std::cmp::min(num_cpus::get() * 8, 64); // Aggressive but bounded parallelism

        Self {
            metrics: Arc::new(RwLock::new(PerformanceMetrics {
                start_time: Instant::now(),
                download_speed: 0.0,
                cache_hit_rate: 0.0,
                memory_usage: 0,
                cpu_usage: 0.0,
                parallel_downloads: 0,
                total_packages: 0,
                processed_packages: 0,
            })),
            http_client: Client::new(),
            download_cache: Arc::new(DashMap::new()),
            max_concurrent_downloads: max_concurrent,
            connection_pool: ConnectionPool::new(max_concurrent),
        }
    }

    pub async fn download_packages_parallel<F, Fut>(
        &self,
        packages: Vec<(String, String)>, // (name, url)
        progress_callback: F,
    ) -> Result<Vec<(String, Bytes)>, crate::errors::NxError>
    where
        F: Fn(String, f64) + Send + Sync + Clone + 'static,
        Fut: std::future::Future<Output = ()> + Send + 'static,
    {
        info!("🚀 Starting parallel download of {} packages", packages.len());

        let semaphore = Arc::new(tokio::sync::Semaphore::new(self.max_concurrent_downloads));
        let results = Arc::new(DashMap::new());
        let total_packages = packages.len();

        // Update metrics
        {
            let mut metrics = self.metrics.write().await;
            metrics.total_packages = total_packages;
            metrics.parallel_downloads = self.max_concurrent_downloads;
        }

        // Create download tasks
        let download_futures: Vec<_> = packages
            .into_iter()
            .enumerate()
            .map(|(index, (name, url))| {
                let semaphore = semaphore.clone();
                let results = results.clone();
                let cache = self.download_cache.clone();
                let pool = &self.connection_pool;
                let progress_callback = progress_callback.clone();
                let metrics = self.metrics.clone();

                async move {
                    let _permit = semaphore.acquire().await.unwrap();

                    // Check cache first
                    if let Some(cached) = cache.get(&url) {
                        if cached.timestamp.elapsed() < Duration::from_hours(1) {
                            debug!("Cache hit for {}", name);
                            results.insert(name.clone(), (name.clone(), cached.data.clone()));
                            progress_callback(name, 1.0);

                            // Update cache hit rate
                            let mut m = metrics.write().await;
                            m.cache_hit_rate = (m.cache_hit_rate * m.processed_packages as f64 + 1.0) / (m.processed_packages + 1) as f64;
                            m.processed_packages += 1;
                            return;
                        }
                    }

                    // Download with retry
                    match pool.download_with_retry(&url, 3).await {
                        Ok(data) => {
                            // Verify integrity if available
                            let integrity = self.calculate_integrity(&data);

                            // Cache the download
                            cache.insert(url.clone(), CachedDownload {
                                data: data.clone(),
                                integrity,
                                timestamp: Instant::now(),
                                size: data.len() as u64,
                            });

                            results.insert(name.clone(), (name.clone(), data.clone()));
                            progress_callback(name.clone(), 1.0);

                            // Update metrics
                            let mut m = metrics.write().await;
                            m.processed_packages += 1;
                            m.download_speed = self.calculate_download_speed(data.len() as u64, m.start_time.elapsed()).await;

                            debug!("Downloaded {} ({} bytes)", name, data.len());
                        }
                        Err(e) => {
                            warn!("Failed to download {}: {}", name, e);
                            progress_callback(name, 0.0);
                        }
                    }
                }
            })
            .collect();

        // Execute downloads with progress tracking
        let progress_tracker = tokio::spawn({
            let metrics = self.metrics.clone();
            async move {
                let mut interval = tokio::time::interval(Duration::from_millis(500));
                loop {
                    interval.tick().await;
                    let m = metrics.read().await;
                    if m.processed_packages >= total_packages {
                        break;
                    }
                    debug!("Download progress: {}/{} packages", m.processed_packages, total_packages);
                }
            }
        });

        // Wait for all downloads to complete
        stream::iter(download_futures)
            .buffer_unordered(self.max_concurrent_downloads)
            .collect::<Vec<_>>()
            .await;

        progress_tracker.abort();

        // Collect results
        let final_results: Vec<(String, Bytes)> = results
            .into_iter()
            .map(|(_, v)| v)
            .collect();

        info!("✅ Completed parallel download of {} packages", final_results.len());
        Ok(final_results)
    }

    fn calculate_integrity(&self, data: &Bytes) -> String {
        let mut hasher = Sha256::new();
        hasher.update(data);
        let hash = hasher.finalize();
        format!("sha256-{}", base64::encode(hash))
    }

    async fn calculate_download_speed(&self, bytes: u64, elapsed: Duration) -> f64 {
        if elapsed.as_secs() > 0 {
            bytes as f64 / elapsed.as_secs_f64()
        } else {
            0.0
        }
    }

    pub async fn extract_packages_parallel(
        &self,
        packages: Vec<(String, Bytes)>,
        extract_dir: &std::path::Path,
    ) -> Result<Vec<String>, crate::errors::NxError> {
        info!("📦 Extracting {} packages in parallel", packages.len());

        // Use rayon for CPU-intensive extraction
        let extract_results: Result<Vec<_>, _> = packages
            .into_par_iter()
            .map(|(name, data)| {
                self.extract_single_package(&name, &data, extract_dir)
            })
            .collect();

        let results = extract_results?;
        info!("✅ Extracted {} packages", results.len());
        Ok(results)
    }

    fn extract_single_package(
        &self,
        name: &str,
        data: &Bytes,
        extract_dir: &std::path::Path,
    ) -> Result<String, crate::errors::NxError> {
        use tar::Archive;
        use flate2::read::GzDecoder;
        use std::io::Cursor;

        let package_dir = extract_dir.join(name);
        std::fs::create_dir_all(&package_dir)?;

        // Extract tarball
        let cursor = Cursor::new(data.as_ref());
        let decoder = GzDecoder::new(cursor);
        let mut archive = Archive::new(decoder);

        archive.unpack(&package_dir)?;

        debug!("Extracted {} to {:?}", name, package_dir);
        Ok(package_dir.to_string_lossy().to_string())
    }
}

    pub async fn optimize_download_strategy(&mut self, packages: &[String]) -> Vec<Vec<String>> {
        // Group packages by size and dependency relationships for optimal download order
        let mut batches = Vec::new();
        let mut current_batch = Vec::new();
        
        // Sort packages by estimated download priority
        let mut sorted_packages = packages.to_vec();
        sorted_packages.sort_by(|a, b| {
            // Prioritize smaller packages and common dependencies
            self.calculate_download_priority(a).cmp(&self.calculate_download_priority(b))
        });

        for package in sorted_packages {
            current_batch.push(package);
            
            if current_batch.len() >= self.max_concurrent_downloads {
                batches.push(current_batch.clone());
                current_batch.clear();
            }
        }
        
        if !current_batch.is_empty() {
            batches.push(current_batch);
        }

        batches
    }

    fn calculate_download_priority(&self, package: &str) -> u32 {
        // Heuristic for download priority
        // Smaller packages and common dependencies get higher priority
        match package {
            p if p.starts_with("@types/") => 1, // Type definitions are usually small
            p if ["lodash", "react", "vue", "express"].contains(&p) => 2, // Common packages
            _ => 3,
        }
    }

    pub async fn parallel_download<F, Fut>(&mut self, packages: Vec<String>, download_fn: F) 
    where
        F: Fn(String) -> Fut + Send + Sync + Clone + 'static,
        Fut: std::future::Future<Output = Result<(), crate::errors::NxError>> + Send + 'static,
    {
        let batches = self.optimize_download_strategy(&packages).await;
        
        for batch in batches {
            let mut batch_tasks = tokio::task::JoinSet::new();
            
            for package in batch {
                let download_fn_clone = download_fn.clone();
                let package_clone = package.clone();
                
                batch_tasks.spawn(async move {
                    let start = Instant::now();
                    let result = download_fn_clone(package_clone.clone()).await;
                    let duration = start.elapsed();
                    
                    debug!("Downloaded {} in {:?}", package_clone, duration);
                    result
                });
            }
            
            // Wait for all downloads in this batch to complete
            while let Some(result) = batch_tasks.join_next().await {
                match result {
                    Ok(Ok(())) => {
                        let mut metrics = self.metrics.write().await;
                        metrics.processed_packages += 1;
                    }
                    Ok(Err(e)) => {
                        debug!("Download failed: {}", e);
                    }
                    Err(e) => {
                        debug!("Task failed: {}", e);
                    }
                }
            }
        }
    }

    pub async fn update_metrics(&self, downloaded_bytes: u64, was_cached: bool) {
        let mut metrics = self.metrics.write().await;
        let elapsed = metrics.start_time.elapsed();
        
        if elapsed.as_secs() > 0 {
            metrics.download_speed = downloaded_bytes as f64 / elapsed.as_secs_f64();
        }
        
        if was_cached {
            metrics.cache_hit_rate = (metrics.cache_hit_rate + 1.0) / 2.0; // Simple moving average
        }
    }

    pub async fn get_performance_report(&self) -> PerformanceReport {
        let metrics = self.metrics.read().await;
        
        PerformanceReport {
            total_time: metrics.start_time.elapsed(),
            download_speed_mbps: metrics.download_speed / 1024.0 / 1024.0,
            cache_hit_rate: metrics.cache_hit_rate,
            packages_per_second: metrics.processed_packages as f64 / metrics.start_time.elapsed().as_secs_f64(),
            parallel_efficiency: (metrics.processed_packages as f64 / self.max_concurrent_downloads as f64).min(1.0),
        }
    }
}

#[derive(Debug)]
pub struct PerformanceReport {
    pub total_time: Duration,
    pub download_speed_mbps: f64,
    pub cache_hit_rate: f64,
    pub packages_per_second: f64,
    pub parallel_efficiency: f64,
}

impl PerformanceReport {
    pub fn display(&self) {
        println!("\n⚡ Performance Report:");
        println!("  🕐 Total time: {:.2}s", self.total_time.as_secs_f64());
        println!("  🚀 Download speed: {:.1} MB/s", self.download_speed_mbps);
        println!("  💾 Cache hit rate: {:.1}%", self.cache_hit_rate * 100.0);
        println!("  📦 Packages/sec: {:.1}", self.packages_per_second);
        println!("  ⚙️  Parallel efficiency: {:.1}%", self.parallel_efficiency * 100.0);
        
        // Performance recommendations
        if self.cache_hit_rate < 0.5 {
            println!("  💡 Tip: Consider warming up the cache for better performance");
        }
        
        if self.parallel_efficiency < 0.7 {
            println!("  💡 Tip: Network might be the bottleneck, consider increasing parallel downloads");
        }
        
        if self.download_speed_mbps > 50.0 {
            println!("  🎉 Excellent download performance!");
        }
    }
}

// Memory-efficient streaming for large packages
pub struct StreamingDownloader {
    chunk_size: usize,
    buffer_pool: Vec<Vec<u8>>,
}

impl StreamingDownloader {
    pub fn new() -> Self {
        Self {
            chunk_size: 64 * 1024, // 64KB chunks
            buffer_pool: Vec::new(),
        }
    }

    pub async fn download_streaming<W: tokio::io::AsyncWrite + Unpin>(
        &mut self,
        url: &str,
        writer: &mut W,
        progress_callback: impl Fn(u64),
    ) -> Result<u64, crate::errors::NxError> {
        use tokio::io::AsyncWriteExt;
        
        let client = reqwest::Client::new();
        let response = client.get(url).send().await?;
        
        let total_size = response.content_length().unwrap_or(0);
        let mut downloaded = 0u64;
        
        let mut stream = response.bytes_stream();
        use futures::StreamExt;
        
        while let Some(chunk) = stream.next().await {
            let chunk = chunk?;
            writer.write_all(&chunk).await?;
            
            downloaded += chunk.len() as u64;
            progress_callback(downloaded);
        }
        
        writer.flush().await?;
        Ok(downloaded)
    }
}

// CPU-optimized operations
pub struct CpuOptimizer;

impl CpuOptimizer {
    pub fn optimize_for_system() -> usize {
        let cpu_count = num_cpus::get();
        let physical_cores = num_cpus::get_physical();
        
        // Use more threads than cores for I/O bound operations
        std::cmp::max(cpu_count * 2, 8).min(32)
    }

    pub async fn parallel_extract(
        &self,
        archives: Vec<(String, Vec<u8>)>,
    ) -> Result<Vec<String>, crate::errors::NxError> {
        use rayon::prelude::*;
        
        let results: Result<Vec<_>, _> = archives
            .into_par_iter()
            .map(|(name, data)| {
                // Extract in parallel using rayon
                self.extract_single_archive(&name, &data)
            })
            .collect();
        
        results
    }

    fn extract_single_archive(&self, name: &str, data: &[u8]) -> Result<String, crate::errors::NxError> {
        // Optimized extraction logic
        debug!("Extracting {} ({} bytes)", name, data.len());
        Ok(name.to_string())
    }
}

// Network optimizations
pub struct NetworkOptimizer {
    connection_pool: reqwest::Client,
}

impl NetworkOptimizer {
    pub fn new() -> Self {
        let client = reqwest::Client::builder()
            .pool_max_idle_per_host(20)
            .pool_idle_timeout(Duration::from_secs(30))
            .timeout(Duration::from_secs(30))
            .tcp_keepalive(Duration::from_secs(60))
            .http2_prior_knowledge()
            .build()
            .unwrap();

        Self {
            connection_pool: client,
        }
    }

    pub async fn download_with_retry(
        &self,
        url: &str,
        max_retries: usize,
    ) -> Result<bytes::Bytes, crate::errors::NxError> {
        let mut last_error = None;
        
        for attempt in 0..=max_retries {
            match self.connection_pool.get(url).send().await {
                Ok(response) if response.status().is_success() => {
                    return Ok(response.bytes().await?);
                }
                Ok(response) => {
                    last_error = Some(crate::errors::NxError::RegistryError {
                        message: format!("HTTP {}", response.status()),
                    });
                }
                Err(e) => {
                    last_error = Some(crate::errors::NxError::HttpError(e));
                }
            }
            
            if attempt < max_retries {
                let delay = Duration::from_millis(100 * (1 << attempt)); // Exponential backoff
                tokio::time::sleep(delay).await;
            }
        }
        
        Err(last_error.unwrap())
    }
}
