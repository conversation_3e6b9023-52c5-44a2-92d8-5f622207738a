{"name": "test-project", "version": "1.0.0", "description": "A new package", "main": "index.js", "scripts": {"hello": "echo \"Hello from nx!\"", "start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"puppeteer": "latest", "lodash": "latest", "express": "latest", "axios": "latest"}, "devDependencies": {}, "peerDependencies": null, "keywords": [], "author": null, "license": "ISC", "repository": null, "homepage": null, "bugs": null, "engines": null, "bin": null}