use indicatif::{MultiProgress, ProgressBar, ProgressStyle};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use crossterm::{
    cursor,
    event::{self, Event, KeyCode},
    execute,
    style::{Color, Print, ResetColor, SetForegroundColor, Stylize},
    terminal::{self, ClearType},
};
use std::io::{self, Write};

pub struct ModernInstallUI {
    multi_progress: MultiProgress,
    main_bar: ProgressBar,
    package_bars: Arc<Mutex<HashMap<String, ProgressBar>>>,
    start_time: Instant,
    stats: Arc<Mutex<InstallStats>>,
}

#[derive(Debug, Default)]
pub struct InstallStats {
    pub total_packages: usize,
    pub installed_packages: usize,
    pub cached_packages: usize,
    pub downloaded_bytes: u64,
    pub cached_bytes: u64,
    pub failed_packages: Vec<String>,
}

impl ModernInstallUI {
    pub fn new(total_packages: usize) -> Self {
        // Disable all logging during installation for clean UI
        std::env::set_var("RUST_LOG", "error");

        let multi_progress = MultiProgress::new();

        // Main progress bar with modern styling
        let main_bar = multi_progress.add(ProgressBar::new(total_packages as u64));
        main_bar.set_style(
            ProgressStyle::default_bar()
                .template("🚀 {msg} [{bar:50.bright_cyan/dim}] {pos}/{len} {elapsed_precise}")
                .unwrap()
                .progress_chars("██▌ "),
        );
        main_bar.set_message("Resolving dependencies");

        Self {
            multi_progress,
            main_bar,
            package_bars: Arc::new(Mutex::new(HashMap::new())),
            start_time: Instant::now(),
            stats: Arc::new(Mutex::new(InstallStats {
                total_packages,
                ..Default::default()
            })),
        }
    }

    pub fn set_phase(&self, phase: &str) {
        self.main_bar.set_message(phase.to_string());
    }

    pub fn show_clean_header(&self) {
        println!("\n{}", "NX Package Manager".bright_cyan().bold());
        println!("{}", "━".repeat(50).dim());
    }

    pub fn start_package_download(&self, package_name: &str, size_bytes: Option<u64>) {
        // Only show progress for larger packages to avoid UI clutter
        if let Some(size) = size_bytes {
            if size > 1024 * 100 { // Only show for packages > 100KB
                let pb = self.multi_progress.add(ProgressBar::new(size));

                pb.set_style(
                    ProgressStyle::default_bar()
                        .template("  {msg} [{bar:25.bright_green/dim}] {bytes}/{total_bytes}")
                        .unwrap()
                        .progress_chars("██▌ "),
                );

                pb.set_message(format!("📦 {}", package_name));
                self.package_bars.lock().unwrap().insert(package_name.to_string(), pb);
            }
        }
    }

    pub fn start_batch_download(&self, count: usize) {
        self.main_bar.set_message(format!("Downloading {} packages", count));
    }

    pub fn update_package_progress(&self, package_name: &str, downloaded_bytes: u64) {
        if let Some(pb) = self.package_bars.lock().unwrap().get(package_name) {
            pb.set_position(downloaded_bytes);
        }
    }

    pub fn finish_package_download(&self, package_name: &str, was_cached: bool, size_bytes: u64) {
        if let Some(pb) = self.package_bars.lock().unwrap().remove(package_name) {
            let icon = if was_cached { "💾" } else { "✅" };
            let source = if was_cached { "cached" } else { "downloaded" };
            
            pb.finish_with_message(format!("{} {} ({:.1} KB {})", 
                icon, 
                package_name, 
                size_bytes as f64 / 1024.0,
                source
            ));
        }

        // Update stats
        {
            let mut stats = self.stats.lock().unwrap();
            stats.installed_packages += 1;
            if was_cached {
                stats.cached_packages += 1;
                stats.cached_bytes += size_bytes;
            } else {
                stats.downloaded_bytes += size_bytes;
            }
        }

        self.main_bar.inc(1);
        self.update_main_message();
    }

    pub fn fail_package(&self, package_name: &str, error: &str) {
        if let Some(pb) = self.package_bars.lock().unwrap().remove(package_name) {
            pb.finish_with_message(format!("❌ {} ({})", package_name, error));
        }

        {
            let mut stats = self.stats.lock().unwrap();
            stats.failed_packages.push(package_name.to_string());
        }

        self.main_bar.inc(1);
        self.update_main_message();
    }

    fn update_main_message(&self) {
        let stats = self.stats.lock().unwrap();
        let elapsed = self.start_time.elapsed();
        
        let message = format!(
            "Installing packages ({} installed, {} cached, {} failed) - {:.1}s",
            stats.installed_packages - stats.cached_packages,
            stats.cached_packages,
            stats.failed_packages.len(),
            elapsed.as_secs_f64()
        );
        
        self.main_bar.set_message(message);
    }

    pub fn show_dependency_tree(&self, tree: &DependencyTree) {
        println!("\n📊 Dependency Tree:");
        self.print_tree_node(&tree.root, 0, true);
    }

    fn print_tree_node(&self, node: &TreeNode, depth: usize, is_last: bool) {
        let prefix = if depth == 0 {
            "".to_string()
        } else {
            let mut p = "  ".repeat(depth - 1);
            p.push_str(if is_last { "└─ " } else { "├─ " });
            p
        };

        let status_icon = match node.status {
            NodeStatus::Installed => "✅",
            NodeStatus::Cached => "💾",
            NodeStatus::Failed => "❌",
            NodeStatus::Pending => "⏳",
        };

        println!("{}{} {}@{}", prefix, status_icon, node.name, node.version);

        for (i, child) in node.children.iter().enumerate() {
            let is_last_child = i == node.children.len() - 1;
            self.print_tree_node(child, depth + 1, is_last_child);
        }
    }

    pub fn finish_installation(&self) {
        let stats = self.stats.lock().unwrap();
        let elapsed = self.start_time.elapsed();

        self.main_bar.finish_and_clear();

        // Clean, minimal summary
        println!("\n{}", "✅ Installation complete!".bright_green().bold());

        let total_mb = (stats.downloaded_bytes + stats.cached_bytes) as f64 / 1024.0 / 1024.0;
        let speed_mbps = if elapsed.as_secs() > 0 {
            (stats.downloaded_bytes as f64 / 1024.0 / 1024.0) / elapsed.as_secs_f64()
        } else {
            0.0
        };

        println!("   {} packages in {:.1}s ({:.1}MB at {:.1}MB/s)",
            stats.total_packages,
            elapsed.as_secs_f64(),
            total_mb,
            speed_mbps
        );

        if stats.cached_packages > 0 {
            println!("   {} packages from cache", stats.cached_packages);
        }

        if !stats.failed_packages.is_empty() {
            println!("   {} {} failed",
                "⚠️".yellow(),
                stats.failed_packages.len()
            );
        }

        println!();
    }

    pub fn show_minimal_progress(&self, current: usize, total: usize, current_package: &str) {
        if total > 0 {
            let percentage = (current as f64 / total as f64 * 100.0) as u8;
            let bar_width = 30;
            let filled = (bar_width as f64 * current as f64 / total as f64) as usize;
            let empty = bar_width - filled;

            print!("\r🚀 Installing [{}{} {}/{}] {}",
                "█".repeat(filled).bright_cyan(),
                " ".repeat(empty),
                current,
                total,
                current_package.trim_start_matches("@types/").chars().take(20).collect::<String>()
            );
            io::stdout().flush().unwrap();

            if current == total {
                println!(); // New line when complete
            }
        }
    }

    pub fn show_conflicts(&self, conflicts: &[crate::advanced_resolver::DependencyConflict]) {
        if !conflicts.is_empty() {
            println!("\n⚠️  Dependency Conflicts:");
            for conflict in conflicts {
                println!("  {} - requested versions: {}, resolved: {}", 
                    conflict.package,
                    conflict.requested_versions.join(", "),
                    conflict.resolved_version
                );
            }
        }
    }

    pub fn show_warnings(&self, warnings: &[String]) {
        if !warnings.is_empty() {
            println!("\n⚠️  Warnings:");
            for warning in warnings {
                println!("  {}", warning);
            }
        }
    }
}

// Dependency tree visualization structures
#[derive(Debug)]
pub struct DependencyTree {
    pub root: TreeNode,
}

#[derive(Debug)]
pub struct TreeNode {
    pub name: String,
    pub version: String,
    pub status: NodeStatus,
    pub children: Vec<TreeNode>,
}

#[derive(Debug)]
pub enum NodeStatus {
    Installed,
    Cached,
    Failed,
    Pending,
}

impl DependencyTree {
    pub fn from_resolved(resolved: &HashMap<String, crate::advanced_resolver::ResolvedDependency>) -> Self {
        // Build tree structure from flat resolved dependencies
        let root = TreeNode {
            name: "root".to_string(),
            version: "0.0.0".to_string(),
            status: NodeStatus::Installed,
            children: resolved.values().map(|dep| TreeNode {
                name: dep.name.clone(),
                version: dep.version.clone(),
                status: NodeStatus::Installed,
                children: Vec::new(), // Simplified - would build full tree in real implementation
            }).collect(),
        };

        Self { root }
    }
}

// Streaming progress for real-time updates
pub struct StreamingProgress {
    sender: tokio::sync::mpsc::UnboundedSender<ProgressUpdate>,
}

#[derive(Debug)]
pub enum ProgressUpdate {
    StartPackage { name: String, size: Option<u64> },
    UpdateProgress { name: String, downloaded: u64 },
    FinishPackage { name: String, cached: bool, size: u64 },
    FailPackage { name: String, error: String },
}

impl StreamingProgress {
    pub fn new() -> (Self, tokio::sync::mpsc::UnboundedReceiver<ProgressUpdate>) {
        let (sender, receiver) = tokio::sync::mpsc::unbounded_channel();
        (Self { sender }, receiver)
    }

    pub fn start_package(&self, name: String, size: Option<u64>) {
        let _ = self.sender.send(ProgressUpdate::StartPackage { name, size });
    }

    pub fn update_progress(&self, name: String, downloaded: u64) {
        let _ = self.sender.send(ProgressUpdate::UpdateProgress { name, downloaded });
    }

    pub fn finish_package(&self, name: String, cached: bool, size: u64) {
        let _ = self.sender.send(ProgressUpdate::FinishPackage { name, cached, size });
    }

    pub fn fail_package(&self, name: String, error: String) {
        let _ = self.sender.send(ProgressUpdate::FailPackage { name, error });
    }
}
