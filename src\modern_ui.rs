use indicatif::{MultiProgress, ProgressBar, ProgressStyle};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tracing::{info, warn};

pub struct ModernInstallUI {
    multi_progress: MultiProgress,
    main_bar: ProgressBar,
    package_bars: Arc<Mutex<HashMap<String, ProgressBar>>>,
    start_time: Instant,
    stats: Arc<Mutex<InstallStats>>,
}

#[derive(Debug, Default)]
pub struct InstallStats {
    pub total_packages: usize,
    pub installed_packages: usize,
    pub cached_packages: usize,
    pub downloaded_bytes: u64,
    pub cached_bytes: u64,
    pub failed_packages: Vec<String>,
}

impl ModernInstallUI {
    pub fn new(total_packages: usize) -> Self {
        let multi_progress = MultiProgress::new();
        
        // Main progress bar
        let main_bar = multi_progress.add(ProgressBar::new(total_packages as u64));
        main_bar.set_style(
            ProgressStyle::default_bar()
                .template("🚀 {msg} [{bar:40.cyan/blue}] {pos}/{len} ({eta})")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏  "),
        );
        main_bar.set_message("Installing packages");

        Self {
            multi_progress,
            main_bar,
            package_bars: Arc::new(Mutex::new(HashMap::new())),
            start_time: Instant::now(),
            stats: Arc::new(Mutex::new(InstallStats {
                total_packages,
                ..Default::default()
            })),
        }
    }

    pub fn start_package_download(&self, package_name: &str, size_bytes: Option<u64>) {
        let pb = self.multi_progress.add(ProgressBar::new(size_bytes.unwrap_or(100)));
        
        pb.set_style(
            ProgressStyle::default_bar()
                .template("  📦 {msg} [{bar:30.green/dim}] {bytes}/{total_bytes} ({bytes_per_sec})")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏  "),
        );
        
        pb.set_message(format!("{}", package_name));
        
        if let Some(size) = size_bytes {
            pb.set_length(size);
        } else {
            pb.set_style(
                ProgressStyle::default_spinner()
                    .template("  📦 {msg} {spinner:.green}")
                    .unwrap()
                    .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]),
            );
            pb.enable_steady_tick(Duration::from_millis(100));
        }

        self.package_bars.lock().unwrap().insert(package_name.to_string(), pb);
    }

    pub fn update_package_progress(&self, package_name: &str, downloaded_bytes: u64) {
        if let Some(pb) = self.package_bars.lock().unwrap().get(package_name) {
            pb.set_position(downloaded_bytes);
        }
    }

    pub fn finish_package_download(&self, package_name: &str, was_cached: bool, size_bytes: u64) {
        if let Some(pb) = self.package_bars.lock().unwrap().remove(package_name) {
            let icon = if was_cached { "💾" } else { "✅" };
            let source = if was_cached { "cached" } else { "downloaded" };
            
            pb.finish_with_message(format!("{} {} ({:.1} KB {})", 
                icon, 
                package_name, 
                size_bytes as f64 / 1024.0,
                source
            ));
        }

        // Update stats
        {
            let mut stats = self.stats.lock().unwrap();
            stats.installed_packages += 1;
            if was_cached {
                stats.cached_packages += 1;
                stats.cached_bytes += size_bytes;
            } else {
                stats.downloaded_bytes += size_bytes;
            }
        }

        self.main_bar.inc(1);
        self.update_main_message();
    }

    pub fn fail_package(&self, package_name: &str, error: &str) {
        if let Some(pb) = self.package_bars.lock().unwrap().remove(package_name) {
            pb.finish_with_message(format!("❌ {} ({})", package_name, error));
        }

        {
            let mut stats = self.stats.lock().unwrap();
            stats.failed_packages.push(package_name.to_string());
        }

        self.main_bar.inc(1);
        self.update_main_message();
    }

    fn update_main_message(&self) {
        let stats = self.stats.lock().unwrap();
        let elapsed = self.start_time.elapsed();
        
        let message = format!(
            "Installing packages ({} installed, {} cached, {} failed) - {:.1}s",
            stats.installed_packages - stats.cached_packages,
            stats.cached_packages,
            stats.failed_packages.len(),
            elapsed.as_secs_f64()
        );
        
        self.main_bar.set_message(message);
    }

    pub fn show_dependency_tree(&self, tree: &DependencyTree) {
        println!("\n📊 Dependency Tree:");
        self.print_tree_node(&tree.root, 0, true);
    }

    fn print_tree_node(&self, node: &TreeNode, depth: usize, is_last: bool) {
        let prefix = if depth == 0 {
            "".to_string()
        } else {
            let mut p = "  ".repeat(depth - 1);
            p.push_str(if is_last { "└─ " } else { "├─ " });
            p
        };

        let status_icon = match node.status {
            NodeStatus::Installed => "✅",
            NodeStatus::Cached => "💾",
            NodeStatus::Failed => "❌",
            NodeStatus::Pending => "⏳",
        };

        println!("{}{} {}@{}", prefix, status_icon, node.name, node.version);

        for (i, child) in node.children.iter().enumerate() {
            let is_last_child = i == node.children.len() - 1;
            self.print_tree_node(child, depth + 1, is_last_child);
        }
    }

    pub fn finish_installation(&self) {
        let stats = self.stats.lock().unwrap();
        let elapsed = self.start_time.elapsed();
        
        self.main_bar.finish_with_message(format!(
            "✅ Installation complete in {:.1}s",
            elapsed.as_secs_f64()
        ));

        // Show final summary
        println!("\n📊 Installation Summary:");
        println!("  ⏱️  Total time: {:.1}s", elapsed.as_secs_f64());
        println!("  📦 Total packages: {}", stats.total_packages);
        println!("  ✅ Installed: {}", stats.installed_packages - stats.cached_packages);
        println!("  💾 From cache: {}", stats.cached_packages);
        
        if !stats.failed_packages.is_empty() {
            println!("  ❌ Failed: {} ({})", 
                stats.failed_packages.len(),
                stats.failed_packages.join(", ")
            );
        }

        let total_mb = (stats.downloaded_bytes + stats.cached_bytes) as f64 / 1024.0 / 1024.0;
        let downloaded_mb = stats.downloaded_bytes as f64 / 1024.0 / 1024.0;
        let cached_mb = stats.cached_bytes as f64 / 1024.0 / 1024.0;
        
        println!("  📊 Data: {:.1}MB total ({:.1}MB downloaded, {:.1}MB cached)", 
            total_mb, downloaded_mb, cached_mb);

        if elapsed.as_secs() > 0 {
            let speed_mbps = downloaded_mb / elapsed.as_secs_f64();
            println!("  🚀 Download speed: {:.1}MB/s", speed_mbps);
        }
    }

    pub fn show_conflicts(&self, conflicts: &[crate::advanced_resolver::DependencyConflict]) {
        if !conflicts.is_empty() {
            println!("\n⚠️  Dependency Conflicts:");
            for conflict in conflicts {
                println!("  {} - requested versions: {}, resolved: {}", 
                    conflict.package,
                    conflict.requested_versions.join(", "),
                    conflict.resolved_version
                );
            }
        }
    }

    pub fn show_warnings(&self, warnings: &[String]) {
        if !warnings.is_empty() {
            println!("\n⚠️  Warnings:");
            for warning in warnings {
                println!("  {}", warning);
            }
        }
    }
}

// Dependency tree visualization structures
#[derive(Debug)]
pub struct DependencyTree {
    pub root: TreeNode,
}

#[derive(Debug)]
pub struct TreeNode {
    pub name: String,
    pub version: String,
    pub status: NodeStatus,
    pub children: Vec<TreeNode>,
}

#[derive(Debug)]
pub enum NodeStatus {
    Installed,
    Cached,
    Failed,
    Pending,
}

impl DependencyTree {
    pub fn from_resolved(resolved: &HashMap<String, crate::advanced_resolver::ResolvedDependency>) -> Self {
        // Build tree structure from flat resolved dependencies
        let root = TreeNode {
            name: "root".to_string(),
            version: "0.0.0".to_string(),
            status: NodeStatus::Installed,
            children: resolved.values().map(|dep| TreeNode {
                name: dep.name.clone(),
                version: dep.version.clone(),
                status: NodeStatus::Installed,
                children: Vec::new(), // Simplified - would build full tree in real implementation
            }).collect(),
        };

        Self { root }
    }
}

// Streaming progress for real-time updates
pub struct StreamingProgress {
    sender: tokio::sync::mpsc::UnboundedSender<ProgressUpdate>,
}

#[derive(Debug)]
pub enum ProgressUpdate {
    StartPackage { name: String, size: Option<u64> },
    UpdateProgress { name: String, downloaded: u64 },
    FinishPackage { name: String, cached: bool, size: u64 },
    FailPackage { name: String, error: String },
}

impl StreamingProgress {
    pub fn new() -> (Self, tokio::sync::mpsc::UnboundedReceiver<ProgressUpdate>) {
        let (sender, receiver) = tokio::sync::mpsc::unbounded_channel();
        (Self { sender }, receiver)
    }

    pub fn start_package(&self, name: String, size: Option<u64>) {
        let _ = self.sender.send(ProgressUpdate::StartPackage { name, size });
    }

    pub fn update_progress(&self, name: String, downloaded: u64) {
        let _ = self.sender.send(ProgressUpdate::UpdateProgress { name, downloaded });
    }

    pub fn finish_package(&self, name: String, cached: bool, size: u64) {
        let _ = self.sender.send(ProgressUpdate::FinishPackage { name, cached, size });
    }

    pub fn fail_package(&self, name: String, error: String) {
        let _ = self.sender.send(ProgressUpdate::FailPackage { name, error });
    }
}
