use crossterm::{
    cursor,
    event::{self, Event, KeyCode, KeyEvent},
    execute,
    style::{Color, Print, ResetColor, SetForegroundColor},
    terminal::{self, ClearType},
};
use std::io::{self, Write};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;

pub struct InteractiveUI {
    terminal_size: (u16, u16),
    current_screen: Screen,
    animation_frame: usize,
    last_update: Instant,
}

#[derive(Debug, Clone)]
pub enum Screen {
    Installation(InstallationScreen),
    DependencyTree(TreeScreen),
    Performance(PerformanceScreen),
    Configuration(ConfigScreen),
}

#[derive(Debug, Clone)]
pub struct InstallationScreen {
    pub packages: Vec<PackageStatus>,
    pub overall_progress: f64,
    pub current_operation: String,
    pub stats: InstallStats,
}

#[derive(Debug, Clone)]
pub struct PackageStatus {
    pub name: String,
    pub version: String,
    pub status: PackageState,
    pub progress: f64,
    pub size: u64,
    pub download_speed: f64,
}

#[derive(Debug, Clone)]
pub enum PackageState {
    Pending,
    Downloading,
    Extracting,
    Linking,
    Complete,
    Failed(String),
    Cached,
}

#[derive(Debug, Clone)]
pub struct InstallStats {
    pub total_packages: usize,
    pub completed: usize,
    pub cached: usize,
    pub failed: usize,
    pub total_size: u64,
    pub downloaded_size: u64,
    pub start_time: Instant,
}

impl InteractiveUI {
    pub fn new() -> Result<Self, io::Error> {
        terminal::enable_raw_mode()?;
        let terminal_size = terminal::size()?;
        
        Ok(Self {
            terminal_size,
            current_screen: Screen::Installation(InstallationScreen {
                packages: Vec::new(),
                overall_progress: 0.0,
                current_operation: "Initializing...".to_string(),
                stats: InstallStats {
                    total_packages: 0,
                    completed: 0,
                    cached: 0,
                    failed: 0,
                    total_size: 0,
                    downloaded_size: 0,
                    start_time: Instant::now(),
                },
            }),
            animation_frame: 0,
            last_update: Instant::now(),
        })
    }

    pub async fn run(&mut self, mut receiver: mpsc::UnboundedReceiver<UIUpdate>) -> Result<(), io::Error> {
        let mut stdout = io::stdout();
        execute!(stdout, terminal::Clear(ClearType::All), cursor::Hide)?;

        let mut update_interval = tokio::time::interval(Duration::from_millis(50));

        loop {
            tokio::select! {
                _ = update_interval.tick() => {
                    self.update_animation();
                    self.render(&mut stdout)?;
                }
                
                update = receiver.recv() => {
                    match update {
                        Some(update) => self.handle_update(update),
                        None => break,
                    }
                }
                
                _ = self.handle_input() => {
                    // Handle keyboard input for interactivity
                }
            }
        }

        execute!(stdout, cursor::Show, ResetColor)?;
        terminal::disable_raw_mode()?;
        Ok(())
    }

    fn update_animation(&mut self) {
        if self.last_update.elapsed() >= Duration::from_millis(100) {
            self.animation_frame = (self.animation_frame + 1) % 10;
            self.last_update = Instant::now();
        }
    }

    fn render(&self, stdout: &mut io::Stdout) -> Result<(), io::Error> {
        execute!(stdout, cursor::MoveTo(0, 0))?;

        match &self.current_screen {
            Screen::Installation(screen) => self.render_installation(stdout, screen)?,
            Screen::DependencyTree(screen) => self.render_tree(stdout, screen)?,
            Screen::Performance(screen) => self.render_performance(stdout, screen)?,
            Screen::Configuration(screen) => self.render_config(stdout, screen)?,
        }

        stdout.flush()?;
        Ok(())
    }

    fn render_installation(&self, stdout: &mut io::Stdout, screen: &InstallationScreen) -> Result<(), io::Error> {
        // Header with animated logo
        let spinner_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"];
        let spinner = spinner_chars[self.animation_frame];
        
        execute!(
            stdout,
            SetForegroundColor(Color::Cyan),
            Print(format!("🚀 NX Package Manager {} {}\n", spinner, screen.current_operation)),
            ResetColor
        )?;

        // Overall progress bar
        let progress_width = (self.terminal_size.0 as f64 * 0.8) as usize;
        let filled = (progress_width as f64 * screen.overall_progress) as usize;
        let empty = progress_width - filled;

        execute!(
            stdout,
            SetForegroundColor(Color::Green),
            Print("Progress: ["),
            Print("█".repeat(filled)),
            SetForegroundColor(Color::DarkGrey),
            Print("░".repeat(empty)),
            SetForegroundColor(Color::Green),
            Print(format!("] {:.1}%\n", screen.overall_progress * 100.0)),
            ResetColor
        )?;

        // Statistics
        let elapsed = screen.stats.start_time.elapsed();
        let speed_mbps = if elapsed.as_secs() > 0 {
            (screen.stats.downloaded_size as f64 / 1024.0 / 1024.0) / elapsed.as_secs_f64()
        } else {
            0.0
        };

        execute!(
            stdout,
            Print(format!(
                "📊 {} total | ✅ {} done | 💾 {} cached | ❌ {} failed | 🚀 {:.1} MB/s\n\n",
                screen.stats.total_packages,
                screen.stats.completed,
                screen.stats.cached,
                screen.stats.failed,
                speed_mbps
            ))
        )?;

        // Package list (show last 10 packages)
        let visible_packages = screen.packages.iter().rev().take(10).collect::<Vec<_>>();
        
        for package in visible_packages.iter().rev() {
            let (icon, color) = match &package.status {
                PackageState::Pending => ("⏳", Color::Yellow),
                PackageState::Downloading => ("📥", Color::Blue),
                PackageState::Extracting => ("📦", Color::Magenta),
                PackageState::Linking => ("🔗", Color::Cyan),
                PackageState::Complete => ("✅", Color::Green),
                PackageState::Failed(_) => ("❌", Color::Red),
                PackageState::Cached => ("💾", Color::DarkGreen),
            };

            execute!(
                stdout,
                SetForegroundColor(color),
                Print(format!("{} {}@{}", icon, package.name, package.version))
            )?;

            // Progress bar for downloading packages
            if matches!(package.status, PackageState::Downloading) {
                let bar_width = 20;
                let filled = (bar_width as f64 * package.progress) as usize;
                let empty = bar_width - filled;
                
                execute!(
                    stdout,
                    Print(format!(" [{}{}] {:.1}%", 
                        "█".repeat(filled),
                        "░".repeat(empty),
                        package.progress * 100.0
                    ))
                )?;

                if package.download_speed > 0.0 {
                    execute!(
                        stdout,
                        Print(format!(" ({:.1} KB/s)", package.download_speed / 1024.0))
                    )?;
                }
            }

            // Error message for failed packages
            if let PackageState::Failed(error) = &package.status {
                execute!(
                    stdout,
                    SetForegroundColor(Color::Red),
                    Print(format!(" - {}", error))
                )?;
            }

            execute!(stdout, Print("\n"), ResetColor)?;
        }

        // Footer with controls
        execute!(
            stdout,
            cursor::MoveTo(0, self.terminal_size.1 - 2),
            SetForegroundColor(Color::DarkGrey),
            Print("Controls: [T] Tree View | [P] Performance | [C] Config | [Q] Quit"),
            ResetColor
        )?;

        Ok(())
    }

    fn render_tree(&self, stdout: &mut io::Stdout, screen: &TreeScreen) -> Result<(), io::Error> {
        execute!(
            stdout,
            SetForegroundColor(Color::Cyan),
            Print("🌳 Dependency Tree\n\n"),
            ResetColor
        )?;

        // Render dependency tree with colors and icons
        for (i, node) in screen.nodes.iter().enumerate() {
            let is_last = i == screen.nodes.len() - 1;
            self.render_tree_node(stdout, node, 0, is_last)?;
        }

        Ok(())
    }

    fn render_tree_node(&self, stdout: &mut io::Stdout, node: &TreeNode, depth: usize, is_last: bool) -> Result<(), io::Error> {
        let prefix = if depth == 0 {
            "".to_string()
        } else {
            let mut p = "  ".repeat(depth - 1);
            p.push_str(if is_last { "└─ " } else { "├─ " });
            p
        };

        let (icon, color) = match node.status {
            NodeStatus::Installed => ("✅", Color::Green),
            NodeStatus::Cached => ("💾", Color::DarkGreen),
            NodeStatus::Failed => ("❌", Color::Red),
            NodeStatus::Pending => ("⏳", Color::Yellow),
        };

        execute!(
            stdout,
            Print(prefix),
            SetForegroundColor(color),
            Print(format!("{} {}@{}", icon, node.name, node.version)),
            ResetColor,
            Print("\n")
        )?;

        for (i, child) in node.children.iter().enumerate() {
            let is_last_child = i == node.children.len() - 1;
            self.render_tree_node(stdout, child, depth + 1, is_last_child)?;
        }

        Ok(())
    }

    fn render_performance(&self, stdout: &mut io::Stdout, screen: &PerformanceScreen) -> Result<(), io::Error> {
        execute!(
            stdout,
            SetForegroundColor(Color::Cyan),
            Print("⚡ Performance Dashboard\n\n"),
            ResetColor
        )?;

        // Real-time performance metrics with graphs
        self.render_metric_bar(stdout, "Download Speed", screen.download_speed, 100.0, "MB/s")?;
        self.render_metric_bar(stdout, "Cache Hit Rate", screen.cache_hit_rate * 100.0, 100.0, "%")?;
        self.render_metric_bar(stdout, "CPU Usage", screen.cpu_usage, 100.0, "%")?;
        self.render_metric_bar(stdout, "Memory Usage", screen.memory_usage as f64 / 1024.0, 1024.0, "MB")?;

        Ok(())
    }

    fn render_metric_bar(&self, stdout: &mut io::Stdout, name: &str, value: f64, max_value: f64, unit: &str) -> Result<(), io::Error> {
        let bar_width = 30;
        let percentage = (value / max_value).min(1.0);
        let filled = (bar_width as f64 * percentage) as usize;
        let empty = bar_width - filled;

        let color = if percentage > 0.8 {
            Color::Red
        } else if percentage > 0.6 {
            Color::Yellow
        } else {
            Color::Green
        };

        execute!(
            stdout,
            Print(format!("{:15}: [", name)),
            SetForegroundColor(color),
            Print("█".repeat(filled)),
            SetForegroundColor(Color::DarkGrey),
            Print("░".repeat(empty)),
            ResetColor,
            Print(format!("] {:.1} {}\n", value, unit))
        )?;

        Ok(())
    }

    fn render_config(&self, stdout: &mut io::Stdout, screen: &ConfigScreen) -> Result<(), io::Error> {
        execute!(
            stdout,
            SetForegroundColor(Color::Cyan),
            Print("⚙️ Configuration\n\n"),
            ResetColor
        )?;

        for (key, value) in &screen.settings {
            execute!(
                stdout,
                Print(format!("{:20}: {}\n", key, value))
            )?;
        }

        Ok(())
    }

    async fn handle_input(&self) -> Result<(), io::Error> {
        if event::poll(Duration::from_millis(0))? {
            if let Event::Key(KeyEvent { code, .. }) = event::read()? {
                match code {
                    KeyCode::Char('q') | KeyCode::Char('Q') => {
                        // Quit application
                    }
                    KeyCode::Char('t') | KeyCode::Char('T') => {
                        // Switch to tree view
                    }
                    KeyCode::Char('p') | KeyCode::Char('P') => {
                        // Switch to performance view
                    }
                    KeyCode::Char('c') | KeyCode::Char('C') => {
                        // Switch to config view
                    }
                    _ => {}
                }
            }
        }
        Ok(())
    }

    fn handle_update(&mut self, update: UIUpdate) {
        match update {
            UIUpdate::PackageStarted { name, version } => {
                if let Screen::Installation(ref mut screen) = self.current_screen {
                    screen.packages.push(PackageStatus {
                        name,
                        version,
                        status: PackageState::Downloading,
                        progress: 0.0,
                        size: 0,
                        download_speed: 0.0,
                    });
                }
            }
            UIUpdate::PackageProgress { name, progress, speed } => {
                if let Screen::Installation(ref mut screen) = self.current_screen {
                    if let Some(package) = screen.packages.iter_mut().find(|p| p.name == name) {
                        package.progress = progress;
                        package.download_speed = speed;
                    }
                }
            }
            UIUpdate::PackageCompleted { name, cached, size } => {
                if let Screen::Installation(ref mut screen) = self.current_screen {
                    if let Some(package) = screen.packages.iter_mut().find(|p| p.name == name) {
                        package.status = if cached { PackageState::Cached } else { PackageState::Complete };
                        package.size = size;
                        screen.stats.completed += 1;
                        if cached {
                            screen.stats.cached += 1;
                        }
                    }
                }
            }
            UIUpdate::OverallProgress { progress, operation } => {
                if let Screen::Installation(ref mut screen) = self.current_screen {
                    screen.overall_progress = progress;
                    screen.current_operation = operation;
                }
            }
        }
    }
}

#[derive(Debug)]
pub enum UIUpdate {
    PackageStarted { name: String, version: String },
    PackageProgress { name: String, progress: f64, speed: f64 },
    PackageCompleted { name: String, cached: bool, size: u64 },
    OverallProgress { progress: f64, operation: String },
}

// Additional screen types
#[derive(Debug, Clone)]
pub struct TreeScreen {
    pub nodes: Vec<TreeNode>,
}

#[derive(Debug, Clone)]
pub struct TreeNode {
    pub name: String,
    pub version: String,
    pub status: NodeStatus,
    pub children: Vec<TreeNode>,
}

#[derive(Debug, Clone)]
pub enum NodeStatus {
    Installed,
    Cached,
    Failed,
    Pending,
}

#[derive(Debug, Clone)]
pub struct PerformanceScreen {
    pub download_speed: f64,
    pub cache_hit_rate: f64,
    pub cpu_usage: f64,
    pub memory_usage: u64,
}

#[derive(Debug, Clone)]
pub struct ConfigScreen {
    pub settings: std::collections::HashMap<String, String>,
}
