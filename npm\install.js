#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const os = require('os');
const path = require('path');
const fs = require('fs');

function getBinaryName() {
  const platform = os.platform();
  const arch = os.arch();

  let binaryName;
  if (platform === 'win32') {
    binaryName = arch === 'arm64' ? 'nx-windows-arm64.exe' : 'nx-windows-x64.exe';
  } else if (platform === 'darwin') {
    binaryName = arch === 'arm64' ? 'nx-macos-arm64' : 'nx-macos-x64';
  } else if (platform === 'linux') {
    binaryName = arch === 'arm64' ? 'nx-linux-arm64' : 'nx-linux-x64';
  } else {
    console.error(`❌ Unsupported platform: ${platform}-${arch}`);
    console.error('Please report this issue at: https://github.com/yourusername/nx/issues');
    process.exit(1);
  }

  return binaryName;
}

function showWelcomeMessage() {
  console.log('🚀 NX Package Manager');
  console.log('   Ultra-fast npm alternative written in Rust');
  console.log('   https://github.com/yourusername/nx\n');
}

function main() {
  const binaryName = getBinaryName();
  const binaryPath = path.join(__dirname, 'bin', binaryName);
  
  // Check if binary exists
  if (!fs.existsSync(binaryPath)) {
    console.error(`Binary not found: ${binaryPath}`);
    console.error('Please report this issue at: https://github.com/justm3sunny/nx/issues');
    process.exit(1);
  }
  
  // Make binary executable on Unix systems
  if (os.platform() !== 'win32') {
    try {
      fs.chmodSync(binaryPath, 0o755);
    } catch (err) {
      console.warn('Warning: Could not make binary executable:', err.message);
    }
  }
  
  // Execute the binary with all arguments
  const args = process.argv.slice(2);
  
  try {
    const child = spawn(binaryPath, args, {
      stdio: 'inherit',
      windowsHide: false
    });
    
    child.on('exit', (code) => {
      process.exit(code || 0);
    });
    
    child.on('error', (err) => {
      console.error('Failed to start nx:', err.message);
      process.exit(1);
    });
    
  } catch (err) {
    console.error('Error executing nx:', err.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { getBinaryName, main };
